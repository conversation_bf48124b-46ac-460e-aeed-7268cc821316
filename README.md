# Memory-Go

Go 版本的内存取证分析平台，基于 Volatility 框架的高性能内存分析服务。

## 特性

- 🚀 高性能的 Go 实现
- 🔄 与 Python 版本 API 完全兼容
- 📊 支持 Volatility 3 和 Volatility 2
- 🔧 结构化日志和监控
- 📦 单一二进制部署
- 🐳 Docker 支持

## 快速开始

### 前置要求

- Go 1.21+
- Volatility 3 和 Volatility 2
- Kafka
- MinIO (或 AWS S3)

### 安装

```bash
# 克隆项目
git clone <repository-url>
cd memory-go

# 安装依赖
make deps

# 构建
make build
```

### 配置

复制并编辑配置文件：

```bash
cp configs/memory_analyzer.yaml.example configs/memory_analyzer.yaml
```

编辑配置文件中的以下部分：
- Kafka 服务器地址
- MinIO/S3 配置
- Volatility 路径

### 运行

```bash
# 开发模式
make run

# 或者使用构建的二进制文件
./bin/memory-go
```

## API 文档

### 启动内存分析

```http
POST /memory_parse
Content-Type: application/json

{
  "file_info": {
    "type": "file",
    "sub_type": "memory_dump",
    "attribute": {
      "os": "Windows"
    },
    "oss": {
      "file": "memory_dump.bin",
      "bucket": "memory-dumps"
    }
  },
  "options": {
    "parse_items": {
      "windows.pslist.PsList": {},
      "windows.dlllist.DllList": {}
    },
    "upload_option": {
      "bucket": "results"
    },
    "kafka_option": {
      "topic": "memory_analysis"
    },
    "show_progress": true
  },
  "extra": {
    "analysis_id": "analysis_123",
    "flow_id": "flow_456"
  }
}
```

### 取消分析

```http
DELETE /memory_parse
Content-Type: application/json

{
  "extra": {
    "flow_id": "flow_456"
  }
}
```

### 获取分析日志

```http
GET /memory_parse/log/{analysis_id}
```

## 开发

### 项目结构

```
memory-go/
├── cmd/                    # 应用程序入口
│   └── server/
├── internal/               # 内部包
│   ├── config/            # 配置管理
│   ├── server/            # HTTP 服务器
│   ├── engine/            # 分析引擎
│   ├── volatility/        # Volatility 执行器
│   ├── processor/         # 结果处理器
│   ├── storage/           # 存储客户端
│   ├── messaging/         # 消息客户端
│   └── models/            # 数据模型
├── pkg/                   # 公共包
│   └── logger/            # 日志包
├── configs/               # 配置文件
├── scripts/               # 脚本文件
└── docs/                  # 文档
```

### 开发命令

```bash
# 安装开发工具
make install-tools

# 开发模式运行（热重载）
make dev

# 运行测试
make test

# 代码格式化
make fmt

# 代码检查
make lint

# 生成测试覆盖率报告
make test-coverage
```

### Docker

```bash
# 构建 Docker 镜像
make docker-build

# 运行 Docker 容器
make docker-run
```

## 部署

### 二进制部署

```bash
# 构建
make build

# 复制配置文件
cp configs/memory_analyzer.yaml /etc/memory-go/

# 运行
./bin/memory-go
```

### Docker 部署

```bash
docker run -d \
  --name memory-go \
  -p 8080:8080 \
  -v /path/to/config:/app/configs \
  -v /path/to/volatility:/usr/local/bin \
  memory-go:latest
```

## 监控

应用程序提供以下监控端点：

- `GET /health` - 健康检查
- `GET /metrics` - Prometheus 指标（待实现）

## 贡献

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。