#!/usr/bin/env python3
"""
测试脚本：只使用vol3插件进行内存分析
"""

import requests
import json
import time

def test_vol3_plugins():
    """测试vol3插件"""
    
    # 服务器地址
    url = "http://localhost:5001/memory_parse"
    
    # 测试数据 - 只包含vol3插件
    test_data = {
        "file_info": {
            "oss": {
                "bucket": "mfp",
                "file": "mem-image/1751944328/win7.vmem"
            },
            "attribute": {
                "os": {
                    "type": "Windows"
                }
            }
        },
        "options": {
            "upload_option": {
                "type": "oss",
                "bucket": "mfp-result",
                "path": "analysis-results",
                "oss": {
                    "endpoint": "http://*************:19000",
                    "access_key": "minioadmin",
                    "secret_key": "minioadmin"
                }
            },
            "kafka_option": {
                "bootstrap_servers": ["*************:19092"],
                "topic": "mfp_result"
            },
            "show_progress": True,
            "parse_items": {
                # 只测试几个基本的vol3插件
                "windows.pslist.PsList": {},
                "windows.netscan.NetScan": {},
                "windows.filescan.FileScan": {},
                "windows.modules.Modules": {},
                "windows.dlllist.DllList": {}
            }
        },
        "extra": {
            "analysis_id": "test_vol3_only_" + str(int(time.time()))
        }
    }
    
    print("=== 测试Vol3插件 ===")
    print(f"请求URL: {url}")
    print(f"测试插件: {list(test_data['options']['parse_items'].keys())}")
    
    try:
        # 发送请求
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 请求成功提交")
            print(f"Flow ID: {result.get('flow_id', 'N/A')}")
            print(f"Task ID: {result.get('task_id', 'N/A')}")
            return True
        else:
            print("❌ 请求失败")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        return False

if __name__ == "__main__":
    success = test_vol3_plugins()
    if success:
        print("\n请检查日志文件查看详细执行情况")
    else:
        print("\n测试失败，请检查服务器状态")
