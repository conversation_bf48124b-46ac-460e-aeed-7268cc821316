[{"Data": null, "Hive Offset": "2.300624904e+09", "Key": "[NONAME]", "Last Write Time": "2023-11-22T08:14:26.000Z", "Name": "A", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.300624904e+09", "Key": "[NONAME]", "Last Write Time": "2023-11-22T08:14:01.000Z", "Name": "MACHINE", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.300624904e+09", "Key": "[NONAME]", "Last Write Time": "2023-11-22T08:14:43.000Z", "Name": "USER", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.30069044e+09", "Key": "\\REGISTRY\\MACHINE\\SYSTEM", "Last Write Time": "2023-11-22T08:03:06.000Z", "Name": "ControlSet001", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.30069044e+09", "Key": "\\REGISTRY\\MACHINE\\SYSTEM", "Last Write Time": "2009-07-14T04:53:14.000Z", "Name": "ControlSet002", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.30069044e+09", "Key": "\\REGISTRY\\MACHINE\\SYSTEM", "Last Write Time": "2023-11-22T08:02:54.000Z", "Name": "MountedDevices", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.30069044e+09", "Key": "\\REGISTRY\\MACHINE\\SYSTEM", "Last Write Time": "2023-11-22T08:18:58.000Z", "Name": "RNG", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.30069044e+09", "Key": "\\REGISTRY\\MACHINE\\SYSTEM", "Last Write Time": "2009-07-14T04:53:15.000Z", "Name": "Select", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.30069044e+09", "Key": "\\REGISTRY\\MACHINE\\SYSTEM", "Last Write Time": "2023-11-22T08:14:04.000Z", "Name": "Setup", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.30069044e+09", "Key": "\\REGISTRY\\MACHINE\\SYSTEM", "Last Write Time": "2023-11-22T08:09:43.000Z", "Name": "WPA", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.30069044e+09", "Key": "\\REGISTRY\\MACHINE\\SYSTEM", "Last Write Time": "2023-11-22T08:13:52.000Z", "Name": "CurrentControlSet", "Type": "Key", "Volatile": "true", "__children": "[]"}, {"Data": null, "Hive Offset": "2.300823504e+09", "Key": "\\REGISTRY\\MACHINE\\HARDWARE", "Last Write Time": "2023-11-22T08:13:52.000Z", "Name": "ACPI", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.300823504e+09", "Key": "\\REGISTRY\\MACHINE\\HARDWARE", "Last Write Time": "2023-11-22T08:13:52.000Z", "Name": "DESCRIPTION", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.300823504e+09", "Key": "\\REGISTRY\\MACHINE\\HARDWARE", "Last Write Time": "2023-11-22T08:14:00.000Z", "Name": "DEVICEMAP", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.300823504e+09", "Key": "\\REGISTRY\\MACHINE\\HARDWARE", "Last Write Time": "2023-11-22T08:13:52.000Z", "Name": "RESOURCEMAP", "Type": "Key", "Volatile": "true", "__children": "[]"}, {"Data": null, "Hive Offset": "2.309113424e+09", "Key": "\\SystemRoot\\System32\\Config\\SECURITY", "Last Write Time": "2023-11-22T08:05:36.000Z", "Name": "Policy", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.309113424e+09", "Key": "\\SystemRoot\\System32\\Config\\SECURITY", "Last Write Time": "2023-11-22T08:14:02.000Z", "Name": "RXACT", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.309113424e+09", "Key": "\\SystemRoot\\System32\\Config\\SECURITY", "Last Write Time": "2023-11-22T08:14:01.000Z", "Name": "SAM", "Type": "Key", "Volatile": "true", "__children": "[]"}, {"Data": null, "Hive Offset": "2.309714112e+09", "Key": "\\SystemRoot\\System32\\Config\\SAM", "Last Write Time": "2023-11-22T08:03:04.000Z", "Name": "SAM", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.324185096e+09", "Key": "\\??\\C:\\Windows\\ServiceProfiles\\NetworkService\\NTUSER.DAT", "Last Write Time": "2009-07-14T04:34:14.000Z", "Name": "AppEvents", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.324185096e+09", "Key": "\\??\\C:\\Windows\\ServiceProfiles\\NetworkService\\NTUSER.DAT", "Last Write Time": "2009-07-14T04:34:14.000Z", "Name": "<PERSON><PERSON><PERSON>", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.324185096e+09", "Key": "\\??\\C:\\Windows\\ServiceProfiles\\NetworkService\\NTUSER.DAT", "Last Write Time": "2009-07-14T04:34:14.000Z", "Name": "Control Panel", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.324185096e+09", "Key": "\\??\\C:\\Windows\\ServiceProfiles\\NetworkService\\NTUSER.DAT", "Last Write Time": "2009-07-14T04:34:14.000Z", "Name": "Environment", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.324185096e+09", "Key": "\\??\\C:\\Windows\\ServiceProfiles\\NetworkService\\NTUSER.DAT", "Last Write Time": "2009-07-14T04:34:14.000Z", "Name": "EUDC", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.324185096e+09", "Key": "\\??\\C:\\Windows\\ServiceProfiles\\NetworkService\\NTUSER.DAT", "Last Write Time": "2009-07-14T04:34:14.000Z", "Name": "Keyboard Layout", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.324185096e+09", "Key": "\\??\\C:\\Windows\\ServiceProfiles\\NetworkService\\NTUSER.DAT", "Last Write Time": "2009-07-14T04:34:14.000Z", "Name": "Network", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.324185096e+09", "Key": "\\??\\C:\\Windows\\ServiceProfiles\\NetworkService\\NTUSER.DAT", "Last Write Time": "2009-07-14T04:34:14.000Z", "Name": "Printers", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.324185096e+09", "Key": "\\??\\C:\\Windows\\ServiceProfiles\\NetworkService\\NTUSER.DAT", "Last Write Time": "2023-11-22T08:13:26.000Z", "Name": "Software", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.324185096e+09", "Key": "\\??\\C:\\Windows\\ServiceProfiles\\NetworkService\\NTUSER.DAT", "Last Write Time": "2009-07-14T04:34:14.000Z", "Name": "System", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.324719984e+09", "Key": "\\??\\C:\\Windows\\ServiceProfiles\\LocalService\\NTUSER.DAT", "Last Write Time": "2009-07-14T04:34:14.000Z", "Name": "AppEvents", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.324719984e+09", "Key": "\\??\\C:\\Windows\\ServiceProfiles\\LocalService\\NTUSER.DAT", "Last Write Time": "2009-07-14T04:34:14.000Z", "Name": "<PERSON><PERSON><PERSON>", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.324719984e+09", "Key": "\\??\\C:\\Windows\\ServiceProfiles\\LocalService\\NTUSER.DAT", "Last Write Time": "2009-07-14T04:34:14.000Z", "Name": "Control Panel", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.324719984e+09", "Key": "\\??\\C:\\Windows\\ServiceProfiles\\LocalService\\NTUSER.DAT", "Last Write Time": "2009-07-14T04:34:14.000Z", "Name": "Environment", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.324719984e+09", "Key": "\\??\\C:\\Windows\\ServiceProfiles\\LocalService\\NTUSER.DAT", "Last Write Time": "2009-07-14T04:34:14.000Z", "Name": "EUDC", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.324719984e+09", "Key": "\\??\\C:\\Windows\\ServiceProfiles\\LocalService\\NTUSER.DAT", "Last Write Time": "2009-07-14T04:34:14.000Z", "Name": "Keyboard Layout", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.324719984e+09", "Key": "\\??\\C:\\Windows\\ServiceProfiles\\LocalService\\NTUSER.DAT", "Last Write Time": "2009-07-14T04:34:14.000Z", "Name": "Network", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.324719984e+09", "Key": "\\??\\C:\\Windows\\ServiceProfiles\\LocalService\\NTUSER.DAT", "Last Write Time": "2009-07-14T04:34:14.000Z", "Name": "Printers", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.324719984e+09", "Key": "\\??\\C:\\Windows\\ServiceProfiles\\LocalService\\NTUSER.DAT", "Last Write Time": "2023-11-22T08:13:26.000Z", "Name": "Software", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.324719984e+09", "Key": "\\??\\C:\\Windows\\ServiceProfiles\\LocalService\\NTUSER.DAT", "Last Write Time": "2009-07-14T04:34:14.000Z", "Name": "System", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.381026512e+09", "Key": "\\SystemRoot\\System32\\Config\\SOFTWARE", "Last Write Time": "2009-07-14T04:37:06.000Z", "Name": "ATI Technologies", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.381026512e+09", "Key": "\\SystemRoot\\System32\\Config\\SOFTWARE", "Last Write Time": "2023-11-22T08:13:31.000Z", "Name": "Classes", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.381026512e+09", "Key": "\\SystemRoot\\System32\\Config\\SOFTWARE", "Last Write Time": "2009-07-14T04:42:26.000Z", "Name": "Clients", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.381026512e+09", "Key": "\\SystemRoot\\System32\\Config\\SOFTWARE", "Last Write Time": "2009-07-14T04:37:06.000Z", "Name": "Intel", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.381026512e+09", "Key": "\\SystemRoot\\System32\\Config\\SOFTWARE", "Last Write Time": "2023-11-22T08:14:13.000Z", "Name": "Microsoft", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.381026512e+09", "Key": "\\SystemRoot\\System32\\Config\\SOFTWARE", "Last Write Time": "2009-07-14T04:37:06.000Z", "Name": "ODBC", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.381026512e+09", "Key": "\\SystemRoot\\System32\\Config\\SOFTWARE", "Last Write Time": "2009-07-14T04:37:24.000Z", "Name": "Policies", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.381026512e+09", "Key": "\\SystemRoot\\System32\\Config\\SOFTWARE", "Last Write Time": "2023-11-22T08:13:16.000Z", "Name": "RegisteredApplications", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.381026512e+09", "Key": "\\SystemRoot\\System32\\Config\\SOFTWARE", "Last Write Time": "2009-07-14T08:41:31.000Z", "Name": "Sonic", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.381026512e+09", "Key": "\\SystemRoot\\System32\\Config\\SOFTWARE", "Last Write Time": "2023-11-22T08:13:18.000Z", "Name": "ThinPrint", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.381026512e+09", "Key": "\\SystemRoot\\System32\\Config\\SOFTWARE", "Last Write Time": "2023-11-22T08:13:25.000Z", "Name": "VMware, Inc.", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.43163956e+09", "Key": "\\Device\\HarddiskVolume1\\Boot\\BCD", "Last Write Time": "2023-11-22T08:14:00.000Z", "Name": "Description", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.43163956e+09", "Key": "\\Device\\HarddiskVolume1\\Boot\\BCD", "Last Write Time": "2023-11-22T08:14:00.000Z", "Name": "Objects", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.468811912e+09", "Key": "\\SystemRoot\\System32\\Config\\DEFAULT", "Last Write Time": "2009-07-14T04:37:41.000Z", "Name": "Control Panel", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.468811912e+09", "Key": "\\SystemRoot\\System32\\Config\\DEFAULT", "Last Write Time": "2009-07-14T04:37:06.000Z", "Name": "Environment", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.468811912e+09", "Key": "\\SystemRoot\\System32\\Config\\DEFAULT", "Last Write Time": "2009-07-14T04:37:06.000Z", "Name": "EUDC", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.468811912e+09", "Key": "\\SystemRoot\\System32\\Config\\DEFAULT", "Last Write Time": "2023-11-22T08:05:59.000Z", "Name": "Keyboard Layout", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.468811912e+09", "Key": "\\SystemRoot\\System32\\Config\\DEFAULT", "Last Write Time": "2023-11-22T08:10:24.000Z", "Name": "Printers", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.468811912e+09", "Key": "\\SystemRoot\\System32\\Config\\DEFAULT", "Last Write Time": "2023-11-22T08:13:26.000Z", "Name": "Software", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.468811912e+09", "Key": "\\SystemRoot\\System32\\Config\\DEFAULT", "Last Write Time": "2009-07-14T04:37:23.000Z", "Name": "SYSTEM", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.553069576e+09", "Key": "\\??\\C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Windows\\UsrClass.dat", "Last Write Time": "2023-11-22T08:10:54.000Z", "Name": "Local Settings", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.554025496e+09", "Key": "\\??\\C:\\System Volume Information\\Syscache.hve", "Last Write Time": "2023-11-22T08:27:44.000Z", "Name": "DefaultObjectStore", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.700050856e+09", "Key": "\\??\\C:\\Users\\<USER>\\ntuser.dat", "Last Write Time": "2023-11-22T08:10:43.000Z", "Name": "AppEvents", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.700050856e+09", "Key": "\\??\\C:\\Users\\<USER>\\ntuser.dat", "Last Write Time": "2023-11-22T08:10:43.000Z", "Name": "<PERSON><PERSON><PERSON>", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.700050856e+09", "Key": "\\??\\C:\\Users\\<USER>\\ntuser.dat", "Last Write Time": "2023-11-22T08:11:01.000Z", "Name": "Control Panel", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.700050856e+09", "Key": "\\??\\C:\\Users\\<USER>\\ntuser.dat", "Last Write Time": "2023-11-22T08:10:43.000Z", "Name": "Environment", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.700050856e+09", "Key": "\\??\\C:\\Users\\<USER>\\ntuser.dat", "Last Write Time": "2023-11-22T08:10:43.000Z", "Name": "EUDC", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.700050856e+09", "Key": "\\??\\C:\\Users\\<USER>\\ntuser.dat", "Last Write Time": "2023-11-22T08:10:47.000Z", "Name": "Identities", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.700050856e+09", "Key": "\\??\\C:\\Users\\<USER>\\ntuser.dat", "Last Write Time": "2023-11-22T08:10:44.000Z", "Name": "Keyboard Layout", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.700050856e+09", "Key": "\\??\\C:\\Users\\<USER>\\ntuser.dat", "Last Write Time": "2023-11-22T08:10:43.000Z", "Name": "Network", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.700050856e+09", "Key": "\\??\\C:\\Users\\<USER>\\ntuser.dat", "Last Write Time": "2023-11-22T08:10:43.000Z", "Name": "Printers", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.700050856e+09", "Key": "\\??\\C:\\Users\\<USER>\\ntuser.dat", "Last Write Time": "2023-11-22T08:14:43.000Z", "Name": "Software", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.700050856e+09", "Key": "\\??\\C:\\Users\\<USER>\\ntuser.dat", "Last Write Time": "2023-11-22T08:10:43.000Z", "Name": "System", "Type": "Key", "Volatile": "false", "__children": "[]"}, {"Data": null, "Hive Offset": "2.700050856e+09", "Key": "\\??\\C:\\Users\\<USER>\\ntuser.dat", "Last Write Time": "2023-11-22T08:14:43.000Z", "Name": "Volatile Environment", "Type": "Key", "Volatile": "true", "__children": "[]"}]