[{"Value": "83 e5 50 00", "Variable": "Kernel Base", "__children": "[]"}, {"Value": "18 50 00", "Variable": "DTB", "__children": "[]"}, {"Value": "file:///home/<USER>/Documents/zorel/sources/memory-go/.venv/lib/python3.13/site-packages/volatility3/symbols/windows/ntkrpamp.pdb/5B308B4ED6464159B87117C711E7340C-2.json.xz", "Variable": "Symbols", "__children": "[]"}, {"Value": "False", "Variable": "Is64Bit", "__children": "[]"}, {"Value": "True", "Variable": "IsPAE", "__children": "[]"}, {"Value": "0 WindowsIntelPAE", "Variable": "layer_name", "__children": "[]"}, {"Value": "1 FileLayer", "Variable": "memory_layer", "__children": "[]"}, {"Value": "83 f7 db e8", "Variable": "KdDebuggerDataBlock", "__children": "[]"}, {"Value": "7600.16385.x86fre.win7_rtm.09071", "Variable": "NTBuildLab", "__children": "[]"}, {"Value": "0", "Variable": "CSDVersion", "__children": "[]"}, {"Value": "83 f7 db c0", "Variable": "KdVersionBlock", "__children": "[]"}, {"Value": "15.7600", "Variable": "Major/Minor", "__children": "[]"}, {"Value": "332", "Variable": "MachineType", "__children": "[]"}, {"Value": "4", "Variable": "KeNumberProcessors", "__children": "[]"}, {"Value": "2023-11-22 08:29:04+00:00", "Variable": "SystemTime", "__children": "[]"}, {"Value": "C:\\Windows", "Variable": "NtSystemRoot", "__children": "[]"}, {"Value": "NtProductWinNt", "Variable": "NtProductType", "__children": "[]"}, {"Value": "6", "Variable": "NtMajorVersion", "__children": "[]"}, {"Value": "1", "Variable": "NtMinorVersion", "__children": "[]"}, {"Value": "6", "Variable": "PE MajorOperatingSystemVersion", "__children": "[]"}, {"Value": "1", "Variable": "PE MinorOperatingSystemVersion", "__children": "[]"}, {"Value": "332", "Variable": "PE Machine", "__children": "[]"}, {"Value": "2009-07-13T23:15:19.000Z", "Variable": "PE TimeDateStamp", "__children": "[]"}]