[{"Block": "3c 08 c0", "PID": "296", "Process": "smss.exe", "Value": "C:\\Windows\\System32", "Variable": "Path", "__children": "[]"}, {"Block": "3c 08 c0", "PID": "296", "Process": "smss.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "3c 08 c0", "PID": "296", "Process": "smss.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "4d 0c c0", "PID": "384", "Process": "csrss.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "4d 0c c0", "PID": "384", "Process": "csrss.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "4d 0c c0", "PID": "384", "Process": "csrss.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "4d 0c c0", "PID": "384", "Process": "csrss.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "4d 0c c0", "PID": "384", "Process": "csrss.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "4d 0c c0", "PID": "384", "Process": "csrss.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "4d 0c c0", "PID": "384", "Process": "csrss.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "4d 0c c0", "PID": "384", "Process": "csrss.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "4d 0c c0", "PID": "384", "Process": "csrss.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "4d 0c c0", "PID": "384", "Process": "csrss.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "4d 0c c0", "PID": "384", "Process": "csrss.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "4d 0c c0", "PID": "384", "Process": "csrss.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "4d 0c c0", "PID": "384", "Process": "csrss.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "4d 0c c0", "PID": "384", "Process": "csrss.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TEMP", "__children": "[]"}, {"Block": "4d 0c c0", "PID": "384", "Process": "csrss.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TMP", "__children": "[]"}, {"Block": "4d 0c c0", "PID": "384", "Process": "csrss.exe", "Value": "SYSTEM", "Variable": "USERNAME", "__children": "[]"}, {"Block": "4d 0c c0", "PID": "384", "Process": "csrss.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TEMP", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TMP", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "SYSTEM", "Variable": "USERNAME", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "2b 0c c0", "PID": "436", "Process": "wininit.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "3e 0c c0", "PID": "444", "Process": "csrss.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "3e 0c c0", "PID": "444", "Process": "csrss.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "3e 0c c0", "PID": "444", "Process": "csrss.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "3e 0c c0", "PID": "444", "Process": "csrss.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "3e 0c c0", "PID": "444", "Process": "csrss.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "3e 0c c0", "PID": "444", "Process": "csrss.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "3e 0c c0", "PID": "444", "Process": "csrss.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "3e 0c c0", "PID": "444", "Process": "csrss.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "3e 0c c0", "PID": "444", "Process": "csrss.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "3e 0c c0", "PID": "444", "Process": "csrss.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "3e 0c c0", "PID": "444", "Process": "csrss.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "3e 0c c0", "PID": "444", "Process": "csrss.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "3e 0c c0", "PID": "444", "Process": "csrss.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "3e 0c c0", "PID": "444", "Process": "csrss.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TEMP", "__children": "[]"}, {"Block": "3e 0c c0", "PID": "444", "Process": "csrss.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TMP", "__children": "[]"}, {"Block": "3e 0c c0", "PID": "444", "Process": "csrss.exe", "Value": "SYSTEM", "Variable": "USERNAME", "__children": "[]"}, {"Block": "3e 0c c0", "PID": "444", "Process": "csrss.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TEMP", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TMP", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "SYSTEM", "Variable": "USERNAME", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "39 0c c0", "PID": "500", "Process": "winlogon.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TEMP", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TMP", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "SYSTEM", "Variable": "USERNAME", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "19 0e 98", "PID": "532", "Process": "services.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "C:\\Windows\\System32", "Variable": "Path", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TEMP", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TMP", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "SYSTEM", "Variable": "USERNAME", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "34 0e 98", "PID": "552", "Process": "lsass.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TEMP", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TMP", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "SYSTEM", "Variable": "USERNAME", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "56 0e 98", "PID": "560", "Process": "lsm.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TEMP", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TMP", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "WORKGROUP", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "ADMIN-PC$", "Variable": "USERNAME", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "0xe0fd0", "PID": "672", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;", "Variable": "Path", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TEMP", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TMP", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "WORKGROUP", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "ADMIN-PC$", "Variable": "USERNAME", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "34 0f d8", "PID": "732", "Process": "vmacthlp.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\NetworkService\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\NetworkService\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "C:\\Windows\\SERVIC~2\\NETWOR~1\\AppData\\Local\\Temp", "Variable": "TEMP", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "C:\\Windows\\SERVIC~2\\NETWOR~1\\AppData\\Local\\Temp", "Variable": "TMP", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "WORKGROUP", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "ADMIN-PC$", "Variable": "USERNAME", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\NetworkService", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "25 10 58", "PID": "780", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\LocalService\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\LocalService\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "C:\\Windows\\SERVIC~2\\LOCALS~1\\AppData\\Local\\Temp", "Variable": "TEMP", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "C:\\Windows\\SERVIC~2\\LOCALS~1\\AppData\\Local\\Temp", "Variable": "TMP", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "NT AUTHORITY", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "LOCAL SERVICE", "Variable": "USERNAME", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\LocalService", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "4b 10 58", "PID": "872", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TEMP", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TMP", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "WORKGROUP", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "ADMIN-PC$", "Variable": "USERNAME", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "19 0f d0", "PID": "908", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TEMP", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TMP", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "WORKGROUP", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "ADMIN-PC$", "Variable": "USERNAME", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "5d 0f d0", "PID": "936", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\LocalService\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\LocalService\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "C:\\Windows\\SERVIC~2\\LOCALS~1\\AppData\\Local\\Temp", "Variable": "TEMP", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "C:\\Windows\\SERVIC~2\\LOCALS~1\\AppData\\Local\\Temp", "Variable": "TMP", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "NT AUTHORITY", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "LOCAL SERVICE", "Variable": "USERNAME", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\LocalService", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "27 10 58", "PID": "10 96", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\NetworkService\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\NetworkService\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "C:\\Windows\\SERVIC~2\\NETWOR~1\\AppData\\Local\\Temp", "Variable": "TEMP", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "C:\\Windows\\SERVIC~2\\NETWOR~1\\AppData\\Local\\Temp", "Variable": "TMP", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "WORKGROUP", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "ADMIN-PC$", "Variable": "USERNAME", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\NetworkService", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "54 10 58", "PID": "12 24", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TEMP", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TMP", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "WORKGROUP", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "ADMIN-PC$", "Variable": "USERNAME", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "31 0f d0", "PID": "14 16", "Process": "spoolsv.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\LocalService\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\LocalService\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "C:\\Windows\\SERVIC~2\\LOCALS~1\\AppData\\Local\\Temp", "Variable": "TEMP", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "C:\\Windows\\SERVIC~2\\LOCALS~1\\AppData\\Local\\Temp", "Variable": "TMP", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "NT AUTHORITY", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "LOCAL SERVICE", "Variable": "USERNAME", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\LocalService", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "57 10 58", "PID": "14 44", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\LocalService\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\LocalService\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "C:\\Windows\\SERVIC~2\\LOCALS~1\\AppData\\Local\\Temp", "Variable": "TEMP", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "C:\\Windows\\SERVIC~2\\LOCALS~1\\AppData\\Local\\Temp", "Variable": "TMP", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "NT AUTHORITY", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "LOCAL SERVICE", "Variable": "USERNAME", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\LocalService", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "57 10 58", "PID": "15 56", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;", "Variable": "Path", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "C:\\Windows\\TEMP", "Variable": "TEMP", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "C:\\Windows\\TEMP", "Variable": "TMP", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "WORKGROUP", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "ADMIN-PC$", "Variable": "USERNAME", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "C:\\Windows\\system32\\config\\systemprofile", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "44 0f d8", "PID": "16 40", "Process": "VGAuthService.", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;", "Variable": "Path", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TEMP", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TMP", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "WORKGROUP", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "ADMIN-PC$", "Variable": "USERNAME", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "2d 0f d8", "PID": "16 96", "Process": "vmtoolsd.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TEMP", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TMP", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "WORKGROUP", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "ADMIN-PC$", "Variable": "USERNAME", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "0x60fd0", "PID": "400", "Process": "WmiPrvSE.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TEMP", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TMP", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "WORKGROUP", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "ADMIN-PC$", "Variable": "USERNAME", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "0xb0fd0", "PID": "616", "Process": "dllhost.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "C:\\Windows\\ServiceProfiles\\NetworkService\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "C:\\Windows\\ServiceProfiles\\NetworkService\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "C:\\Windows\\SERVIC~2\\NETWOR~1\\AppData\\Local\\Temp", "Variable": "TEMP", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "C:\\Windows\\SERVIC~2\\NETWOR~1\\AppData\\Local\\Temp", "Variable": "TMP", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "WORKGROUP", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "ADMIN-PC$", "Variable": "USERNAME", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "C:\\Windows\\ServiceProfiles\\NetworkService", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "0xa1058", "PID": "12 60", "Process": "msdtc.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\LocalService\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\LocalService\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "C:\\Windows\\SERVIC~2\\LOCALS~1\\AppData\\Local\\Temp", "Variable": "TEMP", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "C:\\Windows\\SERVIC~2\\LOCALS~1\\AppData\\Local\\Temp", "Variable": "TMP", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "NT AUTHORITY", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "LOCAL SERVICE", "Variable": "USERNAME", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\LocalService", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "5b 10 58", "PID": "20 52", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TEMP", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TMP", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "WORKGROUP", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "ADMIN-PC$", "Variable": "USERNAME", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "19 0f d0", "PID": "24 08", "Process": "WmiPrvSE.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "C:\\Users\\<USER>\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "C:", "Variable": "HOMEDRIVE", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "\\Users\\admin", "Variable": "HOMEPATH", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "C:\\Users\\<USER>\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "\\\\ADMIN-PC", "Variable": "LOGONSERVER", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "Variable": "TEMP", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "Variable": "TMP", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "admin-PC", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "admin", "Variable": "USERNAME", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "C:\\Users\\<USER>", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "1c 0f e8", "PID": "26 24", "Process": "taskhost.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "C:\\Users\\<USER>\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "C:", "Variable": "HOMEDRIVE", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "\\Users\\admin", "Variable": "HOMEPATH", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "C:\\Users\\<USER>\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "\\\\ADMIN-PC", "Variable": "LOGONSERVER", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "Variable": "TEMP", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "Variable": "TMP", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "admin-PC", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "admin", "Variable": "USERNAME", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "C:\\Users\\<USER>", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "3c 0f e8", "PID": "27 00", "Process": "dwm.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "C:\\Users\\<USER>\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "C:", "Variable": "HOMEDRIVE", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "\\Users\\admin", "Variable": "HOMEPATH", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "C:\\Users\\<USER>\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "\\\\ADMIN-PC", "Variable": "LOGONSERVER", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "<PERSON><PERSON><PERSON>", "Variable": "SESSIONNAME", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "Variable": "TEMP", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "Variable": "TMP", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "admin-PC", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "admin", "Variable": "USERNAME", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "C:\\Users\\<USER>", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "20 10 10", "PID": "27 24", "Process": "explorer.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "C:\\Users\\<USER>\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "C:", "Variable": "HOMEDRIVE", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "\\Users\\admin", "Variable": "HOMEPATH", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "C:\\Users\\<USER>\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "\\\\ADMIN-PC", "Variable": "LOGONSERVER", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "<PERSON><PERSON><PERSON>", "Variable": "SESSIONNAME", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "Variable": "TEMP", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "Variable": "TMP", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "admin-PC", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "admin", "Variable": "USERNAME", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "C:\\Users\\<USER>", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "34 10 10", "PID": "28 40", "Process": "vmtoolsd.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\system32", "Variable": "Path", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "C:\\ProgramData\\Microsoft\\Search\\Data\\Temp\\usgthrsvc", "Variable": "TEMP", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "C:\\ProgramData\\Microsoft\\Search\\Data\\Temp\\usgthrsvc", "Variable": "TMP", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "WORKGROUP", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "ADMIN-PC$", "Variable": "USERNAME", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "C:\\Windows\\system32\\config\\systemprofile", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "28 0f d0", "PID": "30 36", "Process": "SearchIndexer.", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "C:\\Windows\\ServiceProfiles\\NetworkService\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "C:\\Windows\\ServiceProfiles\\NetworkService\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "1", "Variable": "OANOCACHE", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "C:\\Windows\\SERVIC~2\\NETWOR~1\\AppData\\Local\\Temp", "Variable": "TEMP", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "C:\\Windows\\SERVIC~2\\NETWOR~1\\AppData\\Local\\Temp", "Variable": "TMP", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "WORKGROUP", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "ADMIN-PC$", "Variable": "USERNAME", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "C:\\Windows\\ServiceProfiles\\NetworkService", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "3e 10 58", "PID": "32 20", "Process": "wmpnetwk.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\LocalService\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\LocalService\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "C:\\Windows\\SERVIC~2\\LOCALS~1\\AppData\\Local\\Temp", "Variable": "TEMP", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "C:\\Windows\\SERVIC~2\\LOCALS~1\\AppData\\Local\\Temp", "Variable": "TMP", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "NT AUTHORITY", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "LOCAL SERVICE", "Variable": "USERNAME", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "C:\\Windows\\ServiceProfiles\\LocalService", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "19 10 58", "PID": "34 56", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "C:\\Windows\\ServiceProfiles\\NetworkService\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "C:\\Windows\\ServiceProfiles\\NetworkService\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "C:\\Windows\\SERVIC~2\\NETWOR~1\\AppData\\Local\\Temp", "Variable": "TEMP", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "C:\\Windows\\SERVIC~2\\NETWOR~1\\AppData\\Local\\Temp", "Variable": "TMP", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "WORKGROUP", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "ADMIN-PC$", "Variable": "USERNAME", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "C:\\Windows\\ServiceProfiles\\NetworkService", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "3f 10 58", "PID": "32 16", "Process": "sppsvc.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "C:\\ProgramData\\Microsoft\\Windows Defender", "Variable": "MpConfig_ProductAppDataPath", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "AntiSpyware", "Variable": "MpConfig_ProductCodeName", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "c:\\program files\\windows defender", "Variable": "MpConfig_ProductPath", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\Windows Defender", "Variable": "MpConfig_ProductUserAppDataPath", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "CB6D2342-2483-497C-BB3B-946038666FCB", "Variable": "MpConfig_ReportingGUID", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TEMP", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TMP", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "WORKGROUP", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "ADMIN-PC$", "Variable": "USERNAME", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "C:\\Windows\\system32\\config\\systemprofile", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "59 0f d0", "PID": "35 20", "Process": "svchost.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "19 08 c0", "PID": "36 88", "Process": "audiodg.exe", "Value": "C:\\Windows\\System32", "Variable": "Path", "__children": "[]"}, {"Block": "19 08 c0", "PID": "36 88", "Process": "audiodg.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "19 08 c0", "PID": "36 88", "Process": "audiodg.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "C:\\Users\\<USER>\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "C:", "Variable": "HOMEDRIVE", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "\\Users\\admin", "Variable": "HOMEPATH", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "C:\\Users\\<USER>\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "\\\\ADMIN-PC", "Variable": "LOGONSERVER", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "$P$G", "Variable": "PROMPT", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "<PERSON><PERSON><PERSON>", "Variable": "SESSIONNAME", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "Variable": "TEMP", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "Variable": "TMP", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "admin-PC", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "admin", "Variable": "USERNAME", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "C:\\Users\\<USER>", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "4a 10 20", "PID": "38 04", "Process": "cmd.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "3c 0c c0", "PID": "26 08", "Process": "conhost.exe", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "3c 0c c0", "PID": "26 08", "Process": "conhost.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "3c 0c c0", "PID": "26 08", "Process": "conhost.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "3c 0c c0", "PID": "26 08", "Process": "conhost.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "3c 0c c0", "PID": "26 08", "Process": "conhost.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "3c 0c c0", "PID": "26 08", "Process": "conhost.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "3c 0c c0", "PID": "26 08", "Process": "conhost.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "3c 0c c0", "PID": "26 08", "Process": "conhost.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "3c 0c c0", "PID": "26 08", "Process": "conhost.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "3c 0c c0", "PID": "26 08", "Process": "conhost.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "3c 0c c0", "PID": "26 08", "Process": "conhost.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "3c 0c c0", "PID": "26 08", "Process": "conhost.exe", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "3c 0c c0", "PID": "26 08", "Process": "conhost.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "3c 0c c0", "PID": "26 08", "Process": "conhost.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "3c 0c c0", "PID": "26 08", "Process": "conhost.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "3c 0c c0", "PID": "26 08", "Process": "conhost.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TEMP", "__children": "[]"}, {"Block": "3c 0c c0", "PID": "26 08", "Process": "conhost.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TMP", "__children": "[]"}, {"Block": "3c 0c c0", "PID": "26 08", "Process": "conhost.exe", "Value": "SYSTEM", "Variable": "USERNAME", "__children": "[]"}, {"Block": "3c 0c c0", "PID": "26 08", "Process": "conhost.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\system32", "Variable": "Path", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "C:\\ProgramData\\Microsoft\\Search\\Data\\Temp\\usgthrsvc", "Variable": "TEMP", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "C:\\ProgramData\\Microsoft\\Search\\Data\\Temp\\usgthrsvc", "Variable": "TMP", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "WORKGROUP", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "ADMIN-PC$", "Variable": "USERNAME", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "C:\\Windows\\system32\\config\\systemprofile", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "32 10 88", "PID": "33 36", "Process": "SearchProtocol", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "C:\\ProgramData", "Variable": "ALLUSERSPROFILE", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Roaming", "Variable": "APPDATA", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "C:\\Program Files\\Common Files", "Variable": "CommonProgramFiles", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "ADMIN-PC", "Variable": "COMPUTERNAME", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local", "Variable": "LOCALAPPDATA", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\system32", "Variable": "Path", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "C:\\ProgramData", "Variable": "ProgramData", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "C:\\Program Files", "Variable": "ProgramFiles", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "C:\\Users\\<USER>", "Variable": "PUBLIC", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "C:\\ProgramData\\Microsoft\\Search\\Data\\Temp\\usgthrsvc", "Variable": "TEMP", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "C:\\ProgramData\\Microsoft\\Search\\Data\\Temp\\usgthrsvc", "Variable": "TMP", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "WORKGROUP", "Variable": "USERDOMAIN", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "ADMIN-PC$", "Variable": "USERNAME", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "C:\\Windows\\system32\\config\\systemprofile", "Variable": "USERPROFILE", "__children": "[]"}, {"Block": "21 10 88", "PID": "38 48", "Process": "SearchFilterHo", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}, {"Block": "2d 0c c0", "PID": "22 76", "Process": "conhost.exe", "Value": "C:\\Windows\\system32\\cmd.exe", "Variable": "ComSpec", "__children": "[]"}, {"Block": "2d 0c c0", "PID": "22 76", "Process": "conhost.exe", "Value": "NO", "Variable": "FP_NO_HOST_CHECK", "__children": "[]"}, {"Block": "2d 0c c0", "PID": "22 76", "Process": "conhost.exe", "Value": "4", "Variable": "NUMBER_OF_PROCESSORS", "__children": "[]"}, {"Block": "2d 0c c0", "PID": "22 76", "Process": "conhost.exe", "Value": "Windows_NT", "Variable": "OS", "__children": "[]"}, {"Block": "2d 0c c0", "PID": "22 76", "Process": "conhost.exe", "Value": "C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\", "Variable": "Path", "__children": "[]"}, {"Block": "2d 0c c0", "PID": "22 76", "Process": "conhost.exe", "Value": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "Variable": "PATHEXT", "__children": "[]"}, {"Block": "2d 0c c0", "PID": "22 76", "Process": "conhost.exe", "Value": "x86", "Variable": "PROCESSOR_ARCHITECTURE", "__children": "[]"}, {"Block": "2d 0c c0", "PID": "22 76", "Process": "conhost.exe", "Value": "x86 Family 6 Model 165 Stepping 5, GenuineIntel", "Variable": "PROCESSOR_IDENTIFIER", "__children": "[]"}, {"Block": "2d 0c c0", "PID": "22 76", "Process": "conhost.exe", "Value": "6", "Variable": "PROCESSOR_LEVEL", "__children": "[]"}, {"Block": "2d 0c c0", "PID": "22 76", "Process": "conhost.exe", "Value": "a5 05", "Variable": "PROCESSOR_REVISION", "__children": "[]"}, {"Block": "2d 0c c0", "PID": "22 76", "Process": "conhost.exe", "Value": "C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules\\", "Variable": "PSModulePath", "__children": "[]"}, {"Block": "2d 0c c0", "PID": "22 76", "Process": "conhost.exe", "Value": "C:", "Variable": "SystemDrive", "__children": "[]"}, {"Block": "2d 0c c0", "PID": "22 76", "Process": "conhost.exe", "Value": "C:\\Windows", "Variable": "SystemRoot", "__children": "[]"}, {"Block": "2d 0c c0", "PID": "22 76", "Process": "conhost.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TEMP", "__children": "[]"}, {"Block": "2d 0c c0", "PID": "22 76", "Process": "conhost.exe", "Value": "C:\\Windows\\TEMP", "Variable": "TMP", "__children": "[]"}, {"Block": "2d 0c c0", "PID": "22 76", "Process": "conhost.exe", "Value": "SYSTEM", "Variable": "USERNAME", "__children": "[]"}, {"Block": "2d 0c c0", "PID": "22 76", "Process": "conhost.exe", "Value": "C:\\Windows", "Variable": "windir", "__children": "[]"}]