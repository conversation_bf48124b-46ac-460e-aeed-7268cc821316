[{"CreateTime": "2023-11-22T08:14:00.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "432", "ImageFileName": "System", "Offset(V)": "2.236856664e+09", "PID": "4", "PPID": "0", "SessionId": null, "Threads": "109", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:00.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "32", "ImageFileName": "smss.exe", "Offset(V)": "2.258678464e+09", "PID": "296", "PPID": "4", "SessionId": null, "Threads": "2", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:01.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "594", "ImageFileName": "csrss.exe", "Offset(V)": "2.266101744e+09", "PID": "384", "PPID": "368", "SessionId": "0", "Threads": "10", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:01.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "83", "ImageFileName": "wininit.exe", "Offset(V)": "2.26738484e+09", "PID": "436", "PPID": "368", "SessionId": "0", "Threads": "3", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:01.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "192", "ImageFileName": "csrss.exe", "Offset(V)": "2.26739744e+09", "PID": "444", "PPID": "428", "SessionId": "1", "Threads": "10", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:01.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "116", "ImageFileName": "winlogon.exe", "Offset(V)": "2.267992864e+09", "PID": "500", "PPID": "428", "SessionId": "1", "Threads": "3", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:01.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "225", "ImageFileName": "services.exe", "Offset(V)": "2.268137352e+09", "PID": "532", "PPID": "436", "SessionId": "0", "Threads": "7", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:01.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "723", "ImageFileName": "lsass.exe", "Offset(V)": "2.268261152e+09", "PID": "552", "PPID": "436", "SessionId": "0", "Threads": "9", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:01.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "143", "ImageFileName": "lsm.exe", "Offset(V)": "2.268264392e+09", "PID": "560", "PPID": "436", "SessionId": "0", "Threads": "10", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:01.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "366", "ImageFileName": "svchost.exe", "Offset(V)": "2.269107352e+09", "PID": "672", "PPID": "532", "SessionId": "0", "Threads": "10", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:02.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "59", "ImageFileName": "vmacthlp.exe", "Offset(V)": "2.267991288e+09", "PID": "732", "PPID": "532", "SessionId": "0", "Threads": "3", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:02.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "309", "ImageFileName": "svchost.exe", "Offset(V)": "2.269309632e+09", "PID": "780", "PPID": "532", "SessionId": "0", "Threads": "8", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:02.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "631", "ImageFileName": "svchost.exe", "Offset(V)": "2.269564976e+09", "PID": "872", "PPID": "532", "SessionId": "0", "Threads": "27", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:02.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "603", "ImageFileName": "svchost.exe", "Offset(V)": "2.26973216e+09", "PID": "908", "PPID": "532", "SessionId": "0", "Threads": "29", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:02.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "10 80", "ImageFileName": "svchost.exe", "Offset(V)": "2.26977588e+09", "PID": "936", "PPID": "532", "SessionId": "0", "Threads": "51", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:02.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "836", "ImageFileName": "svchost.exe", "Offset(V)": "2.270104896e+09", "PID": "10 96", "PPID": "532", "SessionId": "0", "Threads": "27", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:02.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "509", "ImageFileName": "svchost.exe", "Offset(V)": "2.270285872e+09", "PID": "12 24", "PPID": "532", "SessionId": "0", "Threads": "22", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:02.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "327", "ImageFileName": "spoolsv.exe", "Offset(V)": "2.27101496e+09", "PID": "14 16", "PPID": "532", "SessionId": "0", "Threads": "13", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:03.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "333", "ImageFileName": "svchost.exe", "Offset(V)": "2.271086608e+09", "PID": "14 44", "PPID": "532", "SessionId": "0", "Threads": "19", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:03.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "399", "ImageFileName": "svchost.exe", "Offset(V)": "2.271318064e+09", "PID": "15 56", "PPID": "532", "SessionId": "0", "Threads": "28", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:03.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "89", "ImageFileName": "VGAuthService.", "Offset(V)": "2.271595048e+09", "PID": "16 40", "PPID": "532", "SessionId": "0", "Threads": "3", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:03.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "300", "ImageFileName": "vmtoolsd.exe", "Offset(V)": "2.27183416e+09", "PID": "16 96", "PPID": "532", "SessionId": "0", "Threads": "9", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:04.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "220", "ImageFileName": "WmiPrvSE.exe", "Offset(V)": "2.27287864e+09", "PID": "400", "PPID": "672", "SessionId": "0", "Threads": "11", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:04.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "213", "ImageFileName": "dllhost.exe", "Offset(V)": "2.272920152e+09", "PID": "616", "PPID": "532", "SessionId": "0", "Threads": "15", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:04.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "151", "ImageFileName": "msdtc.exe", "Offset(V)": "2.271234336e+09", "PID": "12 60", "PPID": "532", "SessionId": "0", "Threads": "13", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:04.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "97", "ImageFileName": "svchost.exe", "Offset(V)": "2.2733292e+09", "PID": "20 52", "PPID": "532", "SessionId": "0", "Threads": "7", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:24.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "225", "ImageFileName": "WmiPrvSE.exe", "Offset(V)": "2.269242856e+09", "PID": "24 08", "PPID": "672", "SessionId": "0", "Threads": "10", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:43.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "178", "ImageFileName": "taskhost.exe", "Offset(V)": "2.237508184e+09", "PID": "26 24", "PPID": "532", "SessionId": "1", "Threads": "9", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:43.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "74", "ImageFileName": "dwm.exe", "Offset(V)": "2.274558e+09", "PID": "27 00", "PPID": "908", "SessionId": "1", "Threads": "3", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:43.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "753", "ImageFileName": "explorer.exe", "Offset(V)": "2.274580344e+09", "PID": "27 24", "PPID": "26 88", "SessionId": "1", "Threads": "28", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:43.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "223", "ImageFileName": "vmtoolsd.exe", "Offset(V)": "2.275128648e+09", "PID": "28 40", "PPID": "27 24", "SessionId": "1", "Threads": "9", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:49.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "660", "ImageFileName": "SearchIndexer.", "Offset(V)": "2.270167088e+09", "PID": "30 36", "PPID": "532", "SessionId": "0", "Threads": "12", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:50.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "568", "ImageFileName": "wmpnetwk.exe", "Offset(V)": "2.27574472e+09", "PID": "32 20", "PPID": "532", "SessionId": "0", "Threads": "32", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:14:51.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "366", "ImageFileName": "svchost.exe", "Offset(V)": "2.275979312e+09", "PID": "34 56", "PPID": "532", "SessionId": "0", "Threads": "10", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:16:04.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "153", "ImageFileName": "sppsvc.exe", "Offset(V)": "2.241915208e+09", "PID": "32 16", "PPID": "532", "SessionId": "0", "Threads": "4", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:16:04.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "311", "ImageFileName": "svchost.exe", "Offset(V)": "2.241940216e+09", "PID": "35 20", "PPID": "532", "SessionId": "0", "Threads": "11", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:27:17.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "131", "ImageFileName": "audiodg.exe", "Offset(V)": "2.27192352e+09", "PID": "36 88", "PPID": "872", "SessionId": "0", "Threads": "5", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:27:33.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "21", "ImageFileName": "cmd.exe", "Offset(V)": "2.261862424e+09", "PID": "38 04", "PPID": "27 24", "SessionId": "1", "Threads": "1", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:27:33.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "60", "ImageFileName": "conhost.exe", "Offset(V)": "2.2748544e+09", "PID": "26 08", "PPID": "444", "SessionId": "1", "Threads": "2", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:28:20.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "284", "ImageFileName": "SearchProtocol", "Offset(V)": "2.275627056e+09", "PID": "33 36", "PPID": "30 36", "SessionId": "0", "Threads": "8", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:28:20.000Z", "ExitTime": null, "File output": "Disabled", "Handles": "100", "ImageFileName": "SearchFilterHo", "Offset(V)": "2.237882848e+09", "PID": "38 48", "PPID": "30 36", "SessionId": "0", "Threads": "6", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:29:04.000Z", "ExitTime": "2023-11-22T08:29:04.000Z", "File output": "Disabled", "Handles": null, "ImageFileName": "cmd.exe", "Offset(V)": "2.242285616e+09", "PID": "36 52", "PPID": "16 96", "SessionId": "0", "Threads": "0", "Wow64": "false", "__children": "[]"}, {"CreateTime": "2023-11-22T08:29:04.000Z", "ExitTime": "2023-11-22T08:29:04.000Z", "File output": "Disabled", "Handles": "28", "ImageFileName": "conhost.exe", "Offset(V)": "2.271041472e+09", "PID": "22 76", "PPID": "384", "SessionId": "0", "Threads": "0", "Wow64": "false", "__children": "[]"}]