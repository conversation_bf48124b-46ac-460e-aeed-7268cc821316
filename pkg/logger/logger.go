package logger

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"memory-go/internal/config"

	"github.com/sirupsen/logrus"
)

// Logger 结构化日志器
type Logger struct {
	*logrus.Logger
	config config.LoggingConfig
}

// New 创建新的日志器
func New(config config.LoggingConfig) *Logger {
	logger := logrus.New()

	// 设置日志级别
	level, err := logrus.ParseLevel(config.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	// 设置日志格式
	if config.Format == "json" {
		logger.SetFormatter(&CustomJSONFormatter{
			TimestampFormat: "2006-01-02T15:04:05.000Z07:00",
		})
	} else {
		logger.SetFormatter(&CustomTextFormatter{
			TimestampFormat: "2006-01-02T15:04:05.000Z07:00",
			FullTimestamp:   true,
			DisableColors:   false,
		})
	}

	// 设置输出
	var output io.Writer = os.Stdout
	if config.Output != "" && config.Output != "stdout" {
		if config.Output == "stderr" {
			output = os.Stderr
		} else {
			// 文件输出，确保目录存在
			if err := os.MkdirAll(filepath.Dir(config.Output), 0755); err == nil {
				if file, err := os.OpenFile(config.Output, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666); err == nil {
					output = file
				}
			}
		}
	}
	logger.SetOutput(output)

	// 添加调用者信息钩子
	logger.AddHook(&CallerHook{})

	return &Logger{
		Logger: logger,
		config: config,
	}
}

// WithContext 创建带上下文的日志条目
func (l *Logger) WithContext(requestID, taskID, component string) *logrus.Entry {
	return l.WithFields(logrus.Fields{
		"request_id": requestID,
		"task_id":    taskID,
		"component":  component,
	})
}

// WithError 创建带错误的日志条目
func (l *Logger) WithError(err error) *logrus.Entry {
	return l.Logger.WithError(err)
}

// WithField 创建带单个字段的日志条目
func (l *Logger) WithField(key string, value interface{}) *logrus.Entry {
	return l.Logger.WithField(key, value)
}

// WithFields 创建带多个字段的日志条目
func (l *Logger) WithFields(fields map[string]interface{}) *logrus.Entry {
	return l.Logger.WithFields(fields)
}

// CustomJSONFormatter 自定义 JSON 格式化器
type CustomJSONFormatter struct {
	TimestampFormat string
}

// Format 格式化日志条目
func (f *CustomJSONFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	data := make(logrus.Fields, len(entry.Data)+4)

	// 复制原始数据
	for k, v := range entry.Data {
		data[k] = v
	}

	// 添加标准字段
	data["timestamp"] = entry.Time.Format(f.TimestampFormat)
	data["level"] = entry.Level.String()
	data["message"] = entry.Message

	// 添加调用者信息
	if entry.HasCaller() {
		data["caller"] = fmt.Sprintf("%s:%d", filepath.Base(entry.Caller.File), entry.Caller.Line)
		data["function"] = entry.Caller.Function
	}

	// 添加服务信息
	data["service"] = "memory-go"

	formatter := &logrus.JSONFormatter{}
	serialized, err := formatter.Format(&logrus.Entry{
		Data:    data,
		Time:    entry.Time,
		Level:   entry.Level,
		Message: entry.Message,
	})

	return serialized, err
}

// CustomTextFormatter 自定义文本格式化器
type CustomTextFormatter struct {
	TimestampFormat string
	FullTimestamp   bool
	DisableColors   bool
}

// Format 格式化日志条目
func (f *CustomTextFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	var b strings.Builder

	// 时间戳
	if f.FullTimestamp {
		b.WriteString(entry.Time.Format(f.TimestampFormat))
		b.WriteString(" ")
	}

	// 日志级别
	levelText := strings.ToUpper(entry.Level.String())
	if !f.DisableColors {
		levelColor := getColorByLevel(entry.Level)
		levelText = fmt.Sprintf("\x1b[%dm%s\x1b[0m", levelColor, levelText)
	}
	b.WriteString(fmt.Sprintf("[%s] ", levelText))

	// 调用者信息
	if entry.HasCaller() {
		b.WriteString(fmt.Sprintf("[%s:%d] ", filepath.Base(entry.Caller.File), entry.Caller.Line))
	}

	// 消息
	b.WriteString(entry.Message)

	// 字段
	if len(entry.Data) > 0 {
		b.WriteString(" ")
		for k, v := range entry.Data {
			b.WriteString(fmt.Sprintf("%s=%v ", k, v))
		}
	}

	b.WriteString("\n")
	return []byte(b.String()), nil
}

// getColorByLevel 根据日志级别获取颜色代码
func getColorByLevel(level logrus.Level) int {
	switch level {
	case logrus.DebugLevel:
		return 37 // white
	case logrus.InfoLevel:
		return 36 // cyan
	case logrus.WarnLevel:
		return 33 // yellow
	case logrus.ErrorLevel:
		return 31 // red
	case logrus.FatalLevel, logrus.PanicLevel:
		return 35 // magenta
	default:
		return 37 // white
	}
}

// CallerHook 调用者信息钩子
type CallerHook struct{}

// Levels 返回钩子应用的日志级别
func (hook *CallerHook) Levels() []logrus.Level {
	return logrus.AllLevels
}

// Fire 执行钩子
func (hook *CallerHook) Fire(entry *logrus.Entry) error {
	if entry.Caller == nil {
		// 获取调用者信息
		if pc, file, line, ok := runtime.Caller(8); ok {
			entry.Caller = &runtime.Frame{
				PC:       pc,
				File:     file,
				Line:     line,
				Function: runtime.FuncForPC(pc).Name(),
			}
		}
	}
	return nil
}

// WithRequestID 添加请求 ID 到日志上下文
func (l *Logger) WithRequestID(requestID string) *logrus.Entry {
	return l.WithField("request_id", requestID)
}

// WithTaskID 添加任务 ID 到日志上下文
func (l *Logger) WithTaskID(taskID string) *logrus.Entry {
	return l.WithField("task_id", taskID)
}

// WithComponent 添加组件名到日志上下文
func (l *Logger) WithComponent(component string) *logrus.Entry {
	return l.WithField("component", component)
}

// WithDuration 添加持续时间到日志上下文
func (l *Logger) WithDuration(duration time.Duration) *logrus.Entry {
	return l.WithField("duration", duration.String())
}

// WithUser 添加用户信息到日志上下文
func (l *Logger) WithUser(userID string) *logrus.Entry {
	return l.WithField("user_id", userID)
}

// LogHTTPRequest 记录 HTTP 请求
func (l *Logger) LogHTTPRequest(method, path, userAgent, clientIP string, statusCode int, duration time.Duration, requestID string) {
	l.WithFields(map[string]interface{}{
		"request_id":  requestID,
		"method":      method,
		"path":        path,
		"status_code": statusCode,
		"duration":    duration.String(),
		"user_agent":  userAgent,
		"client_ip":   clientIP,
		"type":        "http_request",
	}).Info("HTTP request processed")
}

// LogTaskStart 记录任务开始
func (l *Logger) LogTaskStart(taskID, taskType string, metadata map[string]interface{}) {
	fields := map[string]interface{}{
		"task_id":   taskID,
		"task_type": taskType,
		"type":      "task_start",
	}
	for k, v := range metadata {
		fields[k] = v
	}
	l.WithFields(fields).Info("Task started")
}

// LogTaskComplete 记录任务完成
func (l *Logger) LogTaskComplete(taskID, taskType string, duration time.Duration, success bool, metadata map[string]interface{}) {
	fields := map[string]interface{}{
		"task_id":   taskID,
		"task_type": taskType,
		"duration":  duration.String(),
		"success":   success,
		"type":      "task_complete",
	}
	for k, v := range metadata {
		fields[k] = v
	}

	if success {
		l.WithFields(fields).Info("Task completed successfully")
	} else {
		l.WithFields(fields).Error("Task completed with errors")
	}
}

// LogSystemEvent 记录系统事件
func (l *Logger) LogSystemEvent(event string, metadata map[string]interface{}) {
	fields := map[string]interface{}{
		"event": event,
		"type":  "system_event",
	}
	for k, v := range metadata {
		fields[k] = v
	}
	l.WithFields(fields).Info("System event")
}

// LogPerformanceMetric 记录性能指标
func (l *Logger) LogPerformanceMetric(metric string, value interface{}, unit string, metadata map[string]interface{}) {
	fields := map[string]interface{}{
		"metric": metric,
		"value":  value,
		"unit":   unit,
		"type":   "performance_metric",
	}
	for k, v := range metadata {
		fields[k] = v
	}
	l.WithFields(fields).Info("Performance metric")
}

// LogSecurityEvent 记录安全事件
func (l *Logger) LogSecurityEvent(event string, severity string, clientIP string, metadata map[string]interface{}) {
	fields := map[string]interface{}{
		"event":     event,
		"severity":  severity,
		"client_ip": clientIP,
		"type":      "security_event",
	}
	for k, v := range metadata {
		fields[k] = v
	}

	switch severity {
	case "high", "critical":
		l.WithFields(fields).Error("Security event")
	case "medium":
		l.WithFields(fields).Warn("Security event")
	default:
		l.WithFields(fields).Info("Security event")
	}
}

// FromContext 从上下文中获取日志器
func FromContext(ctx context.Context) *logrus.Entry {
	if logger, ok := ctx.Value("logger").(*logrus.Entry); ok {
		return logger
	}
	// 返回默认日志器
	return logrus.NewEntry(logrus.StandardLogger())
}

// ToContext 将日志器添加到上下文
func ToContext(ctx context.Context, logger *logrus.Entry) context.Context {
	return context.WithValue(ctx, "logger", logger)
}

// SetLevel 动态设置日志级别
func (l *Logger) SetLevel(level string) error {
	logLevel, err := logrus.ParseLevel(level)
	if err != nil {
		return fmt.Errorf("invalid log level: %s", level)
	}
	l.Logger.SetLevel(logLevel)
	return nil
}

// GetLevel 获取当前日志级别
func (l *Logger) GetLevel() string {
	return l.Logger.GetLevel().String()
}

// IsDebugEnabled 检查是否启用了调试日志
func (l *Logger) IsDebugEnabled() bool {
	return l.Logger.IsLevelEnabled(logrus.DebugLevel)
}

// Close 关闭日志器（如果输出到文件）
func (l *Logger) Close() error {
	if closer, ok := l.Logger.Out.(io.Closer); ok {
		return closer.Close()
	}
	return nil
}
