# Memory-Go Makefile

.PHONY: build run test clean deps docker

# 变量定义
BINARY_NAME=memory-go
MAIN_PATH=./cmd/server
BUILD_DIR=./bin
CONFIG_PATH=./configs/memory_analyzer.yaml

# 构建
build:
	@echo "Building $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	@go build -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)

# 运行
run:
	@echo "Running $(BINARY_NAME)..."
	@go run $(MAIN_PATH)/main.go

# 开发模式运行（带热重载）
dev:
	@echo "Running in development mode..."
	@air -c .air.toml

# 测试
test:
	@echo "Running tests..."
	@go test -v ./...

# 测试覆盖率
test-coverage:
	@echo "Running tests with coverage..."
	@go test -v -coverprofile=coverage.out ./...
	@go tool cover -html=coverage.out -o coverage.html

# 清理
clean:
	@echo "Cleaning..."
	@rm -rf $(BUILD_DIR)
	@rm -f coverage.out coverage.html

# 安装依赖
deps:
	@echo "Installing dependencies..."
	@go mod download
	@go mod tidy

# 格式化代码
fmt:
	@echo "Formatting code..."
	@go fmt ./...

# 代码检查
lint:
	@echo "Running linter..."
	@golangci-lint run

# Docker 构建
docker-build:
	@echo "Building Docker image..."
	@docker build -t memory-go:latest .

# Docker 运行
docker-run:
	@echo "Running Docker container..."
	@docker run -p 8080:8080 -v $(PWD)/configs:/app/configs memory-go:latest

# 生成 API 文档
docs:
	@echo "Generating API documentation..."
	@swag init -g $(MAIN_PATH)/main.go

# 安装开发工具
install-tools:
	@echo "Installing development tools..."
	@go install github.com/cosmtrek/air@latest
	@go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@go install github.com/swaggo/swag/cmd/swag@latest

# 帮助
help:
	@echo "Available commands:"
	@echo "  build         - Build the application"
	@echo "  run           - Run the application"
	@echo "  dev           - Run in development mode with hot reload"
	@echo "  test          - Run tests"
	@echo "  test-coverage - Run tests with coverage report"
	@echo "  clean         - Clean build artifacts"
	@echo "  deps          - Install dependencies"
	@echo "  fmt           - Format code"
	@echo "  lint          - Run linter"
	@echo "  docker-build  - Build Docker image"
	@echo "  docker-run    - Run Docker container"
	@echo "  docs          - Generate API documentation"
	@echo "  install-tools - Install development tools"
	@echo "  help          - Show this help message"