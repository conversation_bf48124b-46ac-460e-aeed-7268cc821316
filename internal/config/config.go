package config

import (
	"fmt"
	"os"
	"strings"

	"gopkg.in/yaml.v3"
)

// Config 系统配置结构体，与 Python 版本保持一致
type Config struct {
	Server     ServerConfig     `yaml:"server"`
	Volatility VolatilityConfig `yaml:"volatility"`
	Kafka      KafkaConfig      `yaml:"kafka"`
	Logging    LoggingConfig    `yaml:"logging"`
	Output     OutputConfig     `yaml:"output"`
	Security   SecurityConfig   `yaml:"security"`
}

type ServerConfig struct {
	Host string `yaml:"host"`
	Port int    `yaml:"port"`
}

func (s ServerConfig) Address() string {
	return fmt.Sprintf("%s:%d", s.Host, s.Port)
}

type VolatilityConfig struct {
	CachePath     string   `yaml:"cache_path"`
	UserCachePath string   `yaml:"user_cache_path"`
	SymbolPaths   []string `yaml:"symbol_paths"`
	Timeout       int      `yaml:"timeout"`
	MaxWorkers    int      `yaml:"max_workers"`
	Vol3Path      string   `yaml:"vol3_path"`
	Vol2Path      string   `yaml:"vol2_path"`
	PythonPath    string   `yaml:"python_path"` // Python解释器路径
}

type KafkaConfig struct {
	BootstrapServers []string          `yaml:"bootstrap_servers"`
	Topics           map[string]string `yaml:"topics"`
}

// AWS/S3 配置已移除，现在从请求参数中动态获取

type LoggingConfig struct {
	Level  string `yaml:"level"`
	Format string `yaml:"format"`
	Output string `yaml:"output"`
}

type OutputConfig struct {
	TempDir string `yaml:"temp_dir"`
}

type SecurityConfig struct {
	EnableAuth     bool     `yaml:"enable_auth"`
	APIKeys        []string `yaml:"api_keys"`
	RateLimit      int      `yaml:"rate_limit"`
	MaxRequestSize int64    `yaml:"max_request_size"`
}

// Load 加载配置文件
func Load(configPath string) (*Config, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	// 设置默认值
	setDefaults(&config)

	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("config validation failed: %w", err)
	}

	return &config, nil
}

// setDefaults 设置默认配置值
func setDefaults(config *Config) {
	if config.Server.Host == "" {
		config.Server.Host = "0.0.0.0"
	}
	if config.Server.Port == 0 {
		config.Server.Port = 8080
	}
	if config.Volatility.Timeout == 0 {
		config.Volatility.Timeout = 3600 // 1 hour
	}
	if config.Volatility.MaxWorkers == 0 {
		config.Volatility.MaxWorkers = 4
	}
	if config.Volatility.Vol3Path == "" {
		config.Volatility.Vol3Path = "vol"
	}
	if config.Volatility.Vol2Path == "" {
		config.Volatility.Vol2Path = "volatility"
	}
	if config.Volatility.PythonPath == "" {
		config.Volatility.PythonPath = "python3"
	}
	if config.Logging.Level == "" {
		config.Logging.Level = "info"
	}
	if config.Logging.Format == "" {
		config.Logging.Format = "json"
	}
	if config.Output.TempDir == "" {
		config.Output.TempDir = "/tmp/memory-go"
	}
	if config.Security.RateLimit == 0 {
		config.Security.RateLimit = 100 // 默认每分钟100个请求
	}
	if config.Security.MaxRequestSize == 0 {
		config.Security.MaxRequestSize = 100 * 1024 * 1024 // 默认100MB
	}
}

// validateConfig 验证配置
func validateConfig(config *Config) error {
	// 验证服务器配置
	if config.Server.Port < 1 || config.Server.Port > 65535 {
		return fmt.Errorf("invalid server port: %d", config.Server.Port)
	}

	// 验证 Volatility 配置
	if config.Volatility.MaxWorkers < 1 || config.Volatility.MaxWorkers > 100 {
		return fmt.Errorf("invalid max_workers: %d (must be between 1 and 100)", config.Volatility.MaxWorkers)
	}
	if config.Volatility.Timeout < 60 || config.Volatility.Timeout > 86400 {
		return fmt.Errorf("invalid timeout: %d (must be between 60 and 86400 seconds)", config.Volatility.Timeout)
	}

	// 验证 Kafka 配置
	if len(config.Kafka.BootstrapServers) == 0 {
		return fmt.Errorf("kafka bootstrap_servers cannot be empty")
	}

	// AWS 配置验证已移除，现在从请求参数中动态获取

	// 验证日志配置
	validLogLevels := []string{"debug", "info", "warn", "error", "fatal"}
	if !contains(validLogLevels, config.Logging.Level) {
		return fmt.Errorf("invalid log level: %s (must be one of: %s)",
			config.Logging.Level, strings.Join(validLogLevels, ", "))
	}

	validLogFormats := []string{"json", "text"}
	if !contains(validLogFormats, config.Logging.Format) {
		return fmt.Errorf("invalid log format: %s (must be one of: %s)",
			config.Logging.Format, strings.Join(validLogFormats, ", "))
	}

	// 验证安全配置
	if config.Security.RateLimit < 0 {
		return fmt.Errorf("invalid rate_limit: %d (must be >= 0)", config.Security.RateLimit)
	}
	if config.Security.MaxRequestSize < 0 {
		return fmt.Errorf("invalid max_request_size: %d (must be >= 0)", config.Security.MaxRequestSize)
	}

	return nil
}

// contains 检查字符串切片是否包含指定值
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// ToMap 将配置转换为 map（用于 API 响应）
func (c *Config) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"server": map[string]interface{}{
			"host": c.Server.Host,
			"port": c.Server.Port,
		},
		"volatility": map[string]interface{}{
			"cache_path":      c.Volatility.CachePath,
			"user_cache_path": c.Volatility.UserCachePath,
			"symbol_paths":    c.Volatility.SymbolPaths,
			"timeout":         c.Volatility.Timeout,
			"max_workers":     c.Volatility.MaxWorkers,
			"vol3_path":       c.Volatility.Vol3Path,
			"vol2_path":       c.Volatility.Vol2Path,
			"python_path":     c.Volatility.PythonPath,
		},
		"kafka": map[string]interface{}{
			"bootstrap_servers": c.Kafka.BootstrapServers,
			"topics":            c.Kafka.Topics,
		},
		// AWS 配置已移除
		"logging": map[string]interface{}{
			"level":  c.Logging.Level,
			"format": c.Logging.Format,
			"output": c.Logging.Output,
		},
		"output": map[string]interface{}{
			"temp_dir": c.Output.TempDir,
		},
		"security": map[string]interface{}{
			"enable_auth":      c.Security.EnableAuth,
			"rate_limit":       c.Security.RateLimit,
			"max_request_size": c.Security.MaxRequestSize,
		},
	}
}
