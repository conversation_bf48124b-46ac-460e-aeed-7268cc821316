package errors

import (
	"fmt"
	"runtime"
	"time"
)

// ErrorType 错误类型
type ErrorType string

const (
	// 系统错误
	ErrorTypeSystem   ErrorType = "system"
	ErrorTypeConfig   ErrorType = "config"
	ErrorTypeNetwork  ErrorType = "network"
	ErrorTypeStorage  ErrorType = "storage"
	ErrorTypeDatabase ErrorType = "database"

	// 业务错误
	ErrorTypeBusiness   ErrorType = "business"
	ErrorTypeValidation ErrorType = "validation"
	ErrorTypeAuth       ErrorType = "auth"
	ErrorTypePermission ErrorType = "permission"

	// 外部服务错误
	ErrorTypeExternal   ErrorType = "external"
	ErrorTypeVolatility ErrorType = "volatility"
	ErrorTypeKafka      ErrorType = "kafka"
	ErrorTypeMinIO      ErrorType = "minio"

	// 任务错误
	ErrorTypeTask      ErrorType = "task"
	ErrorTypeTimeout   ErrorType = "timeout"
	ErrorTypeCancelled ErrorType = "cancelled"
)

// ErrorSeverity 错误严重程度
type ErrorSeverity string

const (
	SeverityLow      ErrorSeverity = "low"
	SeverityMedium   ErrorSeverity = "medium"
	SeverityHigh     ErrorSeverity = "high"
	SeverityCritical ErrorSeverity = "critical"
)

// AppError 应用程序错误
type AppError struct {
	Type       ErrorType              `json:"type"`
	Code       string                 `json:"code"`
	Message    string                 `json:"message"`
	Details    string                 `json:"details,omitempty"`
	Cause      error                  `json:"-"`
	Severity   ErrorSeverity          `json:"severity"`
	Timestamp  time.Time              `json:"timestamp"`
	Context    map[string]interface{} `json:"context,omitempty"`
	StackTrace string                 `json:"stack_trace,omitempty"`
	Retryable  bool                   `json:"retryable"`
	Component  string                 `json:"component"`
}

// Error 实现 error 接口
func (e *AppError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("[%s:%s] %s: %s", e.Type, e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("[%s:%s] %s", e.Type, e.Code, e.Message)
}

// Unwrap 实现 errors.Unwrap 接口
func (e *AppError) Unwrap() error {
	return e.Cause
}

// WithContext 添加上下文信息
func (e *AppError) WithContext(key string, value interface{}) *AppError {
	if e.Context == nil {
		e.Context = make(map[string]interface{})
	}
	e.Context[key] = value
	return e
}

// WithCause 添加原因错误
func (e *AppError) WithCause(cause error) *AppError {
	e.Cause = cause
	return e
}

// WithDetails 添加详细信息
func (e *AppError) WithDetails(details string) *AppError {
	e.Details = details
	return e
}

// New 创建新的应用程序错误
func New(errorType ErrorType, code, message string) *AppError {
	return &AppError{
		Type:      errorType,
		Code:      code,
		Message:   message,
		Severity:  SeverityMedium,
		Timestamp: time.Now(),
		Retryable: false,
		Component: getCallerComponent(),
	}
}

// NewWithSeverity 创建带严重程度的错误
func NewWithSeverity(errorType ErrorType, code, message string, severity ErrorSeverity) *AppError {
	return &AppError{
		Type:      errorType,
		Code:      code,
		Message:   message,
		Severity:  severity,
		Timestamp: time.Now(),
		Retryable: false,
		Component: getCallerComponent(),
	}
}

// Wrap 包装现有错误
func Wrap(err error, errorType ErrorType, code, message string) *AppError {
	appErr := &AppError{
		Type:      errorType,
		Code:      code,
		Message:   message,
		Cause:     err,
		Severity:  SeverityMedium,
		Timestamp: time.Now(),
		Retryable: false,
		Component: getCallerComponent(),
	}

	if err != nil {
		appErr.Details = err.Error()
	}

	return appErr
}

// getCallerComponent 获取调用者组件名
func getCallerComponent() string {
	if pc, _, _, ok := runtime.Caller(3); ok {
		if fn := runtime.FuncForPC(pc); fn != nil {
			name := fn.Name()
			// 提取包名作为组件名
			if idx := len(name) - 1; idx >= 0 {
				for i := idx; i >= 0; i-- {
					if name[i] == '/' {
						return name[i+1:]
					}
				}
			}
			return name
		}
	}
	return "unknown"
}

// 预定义的常见错误

// 系统错误
func NewSystemError(code, message string) *AppError {
	return NewWithSeverity(ErrorTypeSystem, code, message, SeverityHigh)
}

func NewConfigError(code, message string) *AppError {
	return NewWithSeverity(ErrorTypeConfig, code, message, SeverityHigh)
}

func NewNetworkError(code, message string) *AppError {
	return NewWithSeverity(ErrorTypeNetwork, code, message, SeverityMedium).SetRetryable(true)
}

// 业务错误
func NewValidationError(code, message string) *AppError {
	return NewWithSeverity(ErrorTypeValidation, code, message, SeverityLow)
}

func NewAuthError(code, message string) *AppError {
	return NewWithSeverity(ErrorTypeAuth, code, message, SeverityMedium)
}

func NewPermissionError(code, message string) *AppError {
	return NewWithSeverity(ErrorTypePermission, code, message, SeverityMedium)
}

// 外部服务错误
func NewVolatilityError(code, message string) *AppError {
	return NewWithSeverity(ErrorTypeVolatility, code, message, SeverityMedium).SetRetryable(true)
}

func NewKafkaError(code, message string) *AppError {
	return NewWithSeverity(ErrorTypeKafka, code, message, SeverityMedium).SetRetryable(true)
}

func NewMinIOError(code, message string) *AppError {
	return NewWithSeverity(ErrorTypeMinIO, code, message, SeverityMedium).SetRetryable(true)
}

// 任务错误
func NewTaskError(code, message string) *AppError {
	return NewWithSeverity(ErrorTypeTask, code, message, SeverityMedium)
}

func NewTimeoutError(code, message string) *AppError {
	return NewWithSeverity(ErrorTypeTimeout, code, message, SeverityMedium).SetRetryable(true)
}

func NewCancelledError(code, message string) *AppError {
	return NewWithSeverity(ErrorTypeCancelled, code, message, SeverityLow)
}

// SetRetryable 设置错误是否可重试
func (e *AppError) SetRetryable(retryable bool) *AppError {
	e.Retryable = retryable
	return e
}

// SetStackTrace 设置堆栈跟踪
func (e *AppError) SetStackTrace() *AppError {
	buf := make([]byte, 2048)
	n := runtime.Stack(buf, false)
	e.StackTrace = string(buf[:n])
	return e
}

// IsRetryable 检查错误是否可重试
func IsRetryable(err error) bool {
	if appErr, ok := err.(*AppError); ok {
		return appErr.Retryable
	}
	return false
}

// GetErrorType 获取错误类型
func GetErrorType(err error) ErrorType {
	if appErr, ok := err.(*AppError); ok {
		return appErr.Type
	}
	return ErrorTypeSystem
}

// GetErrorSeverity 获取错误严重程度
func GetErrorSeverity(err error) ErrorSeverity {
	if appErr, ok := err.(*AppError); ok {
		return appErr.Severity
	}
	return SeverityMedium
}

// ToHTTPStatus 将错误转换为 HTTP 状态码
func ToHTTPStatus(err error) int {
	if appErr, ok := err.(*AppError); ok {
		switch appErr.Type {
		case ErrorTypeValidation:
			return 400
		case ErrorTypeAuth:
			return 401
		case ErrorTypePermission:
			return 403
		case ErrorTypeBusiness:
			return 400
		case ErrorTypeTimeout:
			return 408
		case ErrorTypeExternal, ErrorTypeVolatility, ErrorTypeKafka, ErrorTypeMinIO:
			return 502
		case ErrorTypeSystem, ErrorTypeConfig, ErrorTypeNetwork, ErrorTypeStorage:
			return 500
		default:
			return 500
		}
	}
	return 500
}

// ToAPIResponse 将错误转换为 API 响应格式
func ToAPIResponse(err error) map[string]interface{} {
	if appErr, ok := err.(*AppError); ok {
		response := map[string]interface{}{
			"error": map[string]interface{}{
				"type":      appErr.Type,
				"code":      appErr.Code,
				"message":   appErr.Message,
				"timestamp": appErr.Timestamp.Format(time.RFC3339),
				"retryable": appErr.Retryable,
			},
		}

		if appErr.Details != "" {
			response["error"].(map[string]interface{})["details"] = appErr.Details
		}

		if appErr.Context != nil && len(appErr.Context) > 0 {
			response["error"].(map[string]interface{})["context"] = appErr.Context
		}

		return response
	}

	// 对于非 AppError，返回通用错误格式
	return map[string]interface{}{
		"error": map[string]interface{}{
			"type":      ErrorTypeSystem,
			"code":      "UNKNOWN_ERROR",
			"message":   err.Error(),
			"timestamp": time.Now().Format(time.RFC3339),
			"retryable": false,
		},
	}
}
