package errors

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"time"
)

// RetryConfig 重试配置
type RetryConfig struct {
	MaxAttempts     int           `json:"max_attempts"`
	InitialDelay    time.Duration `json:"initial_delay"`
	MaxDelay        time.Duration `json:"max_delay"`
	BackoffFactor   float64       `json:"backoff_factor"`
	Jitter          bool          `json:"jitter"`
	RetryableErrors []ErrorType   `json:"retryable_errors"`
}

// DefaultRetryConfig 默认重试配置
var DefaultRetryConfig = RetryConfig{
	MaxAttempts:   3,
	InitialDelay:  100 * time.Millisecond,
	MaxDelay:      30 * time.Second,
	BackoffFactor: 2.0,
	Jitter:        true,
	RetryableErrors: []ErrorType{
		ErrorTypeNetwork,
		ErrorTypeTimeout,
		ErrorTypeExternal,
		ErrorTypeVolatility,
		ErrorTypeKafka,
		ErrorTypeMinIO,
	},
}

// RetryableFunc 可重试的函数类型
type RetryableFunc func() error

// RetryWithConfig 使用指定配置重试执行函数
func RetryWithConfig(ctx context.Context, config RetryConfig, fn RetryableFunc) error {
	var lastErr error

	for attempt := 1; attempt <= config.MaxAttempts; attempt++ {
		// 执行函数
		err := fn()
		if err == nil {
			return nil // 成功，无需重试
		}

		lastErr = err

		// 检查是否为最后一次尝试
		if attempt == config.MaxAttempts {
			break
		}

		// 检查错误是否可重试
		if !isRetryableError(err, config.RetryableErrors) {
			break
		}

		// 计算延迟时间
		delay := calculateDelay(attempt-1, config)

		// 等待重试
		select {
		case <-ctx.Done():
			return fmt.Errorf("retry cancelled: %w", ctx.Err())
		case <-time.After(delay):
			// 继续重试
		}
	}

	return fmt.Errorf("retry failed after %d attempts: %w", config.MaxAttempts, lastErr)
}

// Retry 使用默认配置重试执行函数
func Retry(ctx context.Context, fn RetryableFunc) error {
	return RetryWithConfig(ctx, DefaultRetryConfig, fn)
}

// RetryWithAttempts 指定重试次数
func RetryWithAttempts(ctx context.Context, maxAttempts int, fn RetryableFunc) error {
	config := DefaultRetryConfig
	config.MaxAttempts = maxAttempts
	return RetryWithConfig(ctx, config, fn)
}

// isRetryableError 检查错误是否可重试
func isRetryableError(err error, retryableErrors []ErrorType) bool {
	// 首先检查是否为 AppError 且标记为可重试
	if appErr, ok := err.(*AppError); ok {
		if appErr.Retryable {
			return true
		}

		// 检查错误类型是否在可重试列表中
		for _, retryableType := range retryableErrors {
			if appErr.Type == retryableType {
				return true
			}
		}
	}

	return false
}

// calculateDelay 计算延迟时间
func calculateDelay(attempt int, config RetryConfig) time.Duration {
	// 指数退避
	delay := float64(config.InitialDelay) * math.Pow(config.BackoffFactor, float64(attempt))

	// 限制最大延迟
	if delay > float64(config.MaxDelay) {
		delay = float64(config.MaxDelay)
	}

	// 添加抖动
	if config.Jitter {
		jitter := rand.Float64() * 0.1 * delay // 10% 抖动
		delay += jitter
	}

	return time.Duration(delay)
}

// CircuitBreakerState 熔断器状态
type CircuitBreakerState int

const (
	StateClosed CircuitBreakerState = iota
	StateOpen
	StateHalfOpen
)

// CircuitBreaker 熔断器
type CircuitBreaker struct {
	maxFailures     int
	resetTimeout    time.Duration
	state           CircuitBreakerState
	failures        int
	lastFailureTime time.Time
	mutex           chan struct{}
}

// NewCircuitBreaker 创建新的熔断器
func NewCircuitBreaker(maxFailures int, resetTimeout time.Duration) *CircuitBreaker {
	return &CircuitBreaker{
		maxFailures:  maxFailures,
		resetTimeout: resetTimeout,
		state:        StateClosed,
		mutex:        make(chan struct{}, 1),
	}
}

// Execute 执行函数，带熔断器保护
func (cb *CircuitBreaker) Execute(fn RetryableFunc) error {
	// 获取锁
	cb.mutex <- struct{}{}
	defer func() { <-cb.mutex }()

	// 检查熔断器状态
	if cb.state == StateOpen {
		if time.Since(cb.lastFailureTime) > cb.resetTimeout {
			cb.state = StateHalfOpen
			cb.failures = 0
		} else {
			return NewSystemError("CIRCUIT_BREAKER_OPEN", "Circuit breaker is open")
		}
	}

	// 执行函数
	err := fn()

	if err != nil {
		cb.onFailure()
		return err
	}

	cb.onSuccess()
	return nil
}

// onSuccess 成功时的处理
func (cb *CircuitBreaker) onSuccess() {
	cb.failures = 0
	cb.state = StateClosed
}

// onFailure 失败时的处理
func (cb *CircuitBreaker) onFailure() {
	cb.failures++
	cb.lastFailureTime = time.Now()

	if cb.failures >= cb.maxFailures {
		cb.state = StateOpen
	}
}

// GetState 获取熔断器状态
func (cb *CircuitBreaker) GetState() CircuitBreakerState {
	cb.mutex <- struct{}{}
	defer func() { <-cb.mutex }()
	return cb.state
}

// GetFailures 获取失败次数
func (cb *CircuitBreaker) GetFailures() int {
	cb.mutex <- struct{}{}
	defer func() { <-cb.mutex }()
	return cb.failures
}

// Reset 重置熔断器
func (cb *CircuitBreaker) Reset() {
	cb.mutex <- struct{}{}
	defer func() { <-cb.mutex }()

	cb.state = StateClosed
	cb.failures = 0
	cb.lastFailureTime = time.Time{}
}
