package storage

import (
	"context"
	"fmt"
	"io"
	"os"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"

	"memory-go/pkg/logger"
)

// MinIOConfig MinIO 配置
type MinIOConfig struct {
	Endpoint        string
	AccessKeyID     string
	SecretAccessKey string
	UseSSL          bool
	Region          string
}

// MinIOProvider MinIO 存储提供者
type MinIOProvider struct {
	client     *s3.S3
	uploader   *s3manager.Uploader
	downloader *s3manager.Downloader
	region     string
	logger     *logger.Logger
}

// NewMinIOProvider 创建 MinIO 存储提供者
func NewMinIOProvider(cfg MinIOConfig, log *logger.Logger) (*MinIOProvider, error) {
	// 创建 AWS 会话
	sess, err := session.NewSession(&aws.Config{
		Region:           aws.String(cfg.Region),
		Endpoint:         aws.String(cfg.Endpoint),
		S3ForcePathStyle: aws.Bool(true), // MinIO 需要路径风格
		DisableSSL:       aws.Bool(!cfg.UseSSL),
		Credentials: credentials.NewStaticCredentials(
			cfg.AccessKeyID,
			cfg.SecretAccessKey,
			"", // token
		),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create AWS session: %w", err)
	}

	// 创建 S3 客户端
	client := s3.New(sess)

	// 创建上传和下载管理器
	uploader := s3manager.NewUploader(sess)
	downloader := s3manager.NewDownloader(sess)

	provider := &MinIOProvider{
		client:     client,
		uploader:   uploader,
		downloader: downloader,
		region:     cfg.Region,
		logger:     log,
	}

	log.WithFields(map[string]interface{}{
		"endpoint": cfg.Endpoint,
		"region":   cfg.Region,
		"use_ssl":  cfg.UseSSL,
	}).Info("MinIO provider initialized successfully")

	return provider, nil
}

// GetFileStream 获取文件流，不创建临时文件
func (p *MinIOProvider) GetFileStream(ctx context.Context, bucket, key string) (io.ReadCloser, error) {
	// 直接获取对象流
	result, err := p.client.GetObjectWithContext(ctx, &s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	})

	if err != nil {
		p.logger.WithError(err).WithFields(map[string]interface{}{
			"bucket": bucket,
			"key":    key,
		}).Error("Failed to get file stream from MinIO")
		return nil, fmt.Errorf("failed to get file stream: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"bucket": bucket,
		"key":    key,
	}).Info("File stream opened successfully")

	return result.Body, nil
}

// DownloadFile 下载文件到本地临时文件
func (p *MinIOProvider) DownloadFile(ctx context.Context, bucket, key string) (string, error) {
	// 创建临时文件
	tempFile, err := os.CreateTemp("", "download_*")
	if err != nil {
		return "", fmt.Errorf("failed to create temp file: %w", err)
	}
	tempPath := tempFile.Name()
	defer tempFile.Close()

	// 下载文件
	_, err = p.downloader.DownloadWithContext(ctx, tempFile, &s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		os.Remove(tempPath) // 清理临时文件
		return "", fmt.Errorf("failed to download file: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"bucket":     bucket,
		"key":        key,
		"local_path": tempPath,
	}).Info("File downloaded successfully")

	return tempPath, nil
}

// UploadFile 上传文件
func (p *MinIOProvider) UploadFile(ctx context.Context, bucket, key string, reader io.Reader, size int64, options *UploadOptions) (string, error) {
	input := &s3manager.UploadInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
		Body:   reader,
	}

	// 设置内容类型
	if options != nil && options.ContentType != "" {
		input.ContentType = aws.String(options.ContentType)
	}

	// 设置元数据
	if options != nil && len(options.Metadata) > 0 {
		metadata := make(map[string]*string)
		for k, v := range options.Metadata {
			metadata[k] = aws.String(v)
		}
		input.Metadata = metadata
	}

	// 上传文件
	result, err := p.uploader.UploadWithContext(ctx, input)
	if err != nil {
		return "", fmt.Errorf("failed to upload file: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"bucket":   bucket,
		"key":      key,
		"location": result.Location,
		"size":     size,
	}).Info("File uploaded successfully")

	return result.Location, nil
}

// UploadOptions 上传选项
type UploadOptions struct {
	ContentType string
	Metadata    map[string]string
}

// GetObjectInfo 获取对象信息
func (p *MinIOProvider) GetObjectInfo(ctx context.Context, bucket, key string) (*s3.HeadObjectOutput, error) {
	result, err := p.client.HeadObjectWithContext(ctx, &s3.HeadObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get object info: %w", err)
	}

	return result, nil
}
