package storage

import (
	"context"
	"io"
)

// StorageProvider 存储提供者接口
type StorageProvider interface {
	// Upload 上传文件
	Upload(ctx context.Context, key string, data io.Reader, size int64, metadata map[string]string) error

	// Download 下载文件
	Download(ctx context.Context, key string) (io.ReadCloser, error)

	// Delete 删除文件
	Delete(ctx context.Context, key string) error

	// Exists 检查文件是否存在
	Exists(ctx context.Context, key string) (bool, error)

	// List 列出文件
	List(ctx context.Context, prefix string) ([]string, error)

	// GetInfo 获取文件信息
	GetInfo(ctx context.Context, key string) (*FileInfo, error)

	// Close 关闭连接
	Close() error
}

// FileInfo 文件信息
type FileInfo struct {
	Key          string            `json:"key"`
	Size         int64             `json:"size"`
	LastModified string            `json:"last_modified"`
	ETag         string            `json:"etag"`
	ContentType  string            `json:"content_type"`
	Metadata     map[string]string `json:"metadata"`
}

// 移除重复的 UploadOptions 定义，使用 minio_provider.go 中的定义

// DownloadOptions 下载选项
type DownloadOptions struct {
	Range string `json:"range"` // 字节范围，如 "bytes=0-1023"
}

// ListOptions 列表选项
type ListOptions struct {
	Prefix    string `json:"prefix"`
	Delimiter string `json:"delimiter"`
	MaxKeys   int    `json:"max_keys"`
	Marker    string `json:"marker"`
}
