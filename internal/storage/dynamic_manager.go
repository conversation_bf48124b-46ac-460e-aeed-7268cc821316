package storage

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"

	"memory-go/internal/models"
	"memory-go/pkg/logger"
)

// DynamicManager 动态存储管理器，根据请求参数创建存储客户端
type DynamicManager struct {
	logger *logger.Logger
}

// NewDynamicManager 创建动态存储管理器
func NewDynamicManager(logger *logger.Logger) *DynamicManager {
	return &DynamicManager{
		logger: logger,
	}
}

// createMinIOProvider 根据配置创建 MinIO 提供者
func (dm *DynamicManager) createMinIOProvider(config models.OSSConfig) (*MinIOProvider, error) {
	// 构建 endpoint URL
	protocol := "http"
	if config.SSL {
		protocol = "https"
	}
	endpoint := fmt.Sprintf("%s://%s", protocol, config.Host)

	minioConfig := MinIOConfig{
		Endpoint:        endpoint,
		AccessKeyID:     config.AK,
		SecretAccessKey: config.SK,
		UseSSL:          config.SSL,
		Region:          "us-east-1", // 默认区域
	}

	return NewMinIOProvider(minioConfig, dm.logger)
}

// DownloadFileToTemp 流式下载文件到临时位置，用完即删
func (dm *DynamicManager) DownloadFileToTemp(ctx context.Context, fileInfo *models.FileInfo) (string, func(), error) {
	// 创建 MinIO 提供者
	provider, err := dm.createMinIOProvider(fileInfo.DownloadOption.OSS)
	if err != nil {
		return "", nil, fmt.Errorf("failed to create MinIO provider: %w", err)
	}

	// 获取文件流
	stream, err := provider.GetFileStream(ctx, fileInfo.OSS.Bucket, fileInfo.OSS.File)
	if err != nil {
		return "", nil, err
	}
	defer stream.Close()

	// 创建临时文件（使用更好的命名）
	tempFile, err := os.CreateTemp("", fmt.Sprintf("memory_dump_%s_*.bin", fileInfo.OSS.Bucket))
	if err != nil {
		return "", nil, fmt.Errorf("failed to create temp file: %w", err)
	}
	tempPath := tempFile.Name()

	// 流式复制数据
	_, err = io.Copy(tempFile, stream)
	tempFile.Close()

	if err != nil {
		os.Remove(tempPath) // 清理失败的文件
		return "", nil, fmt.Errorf("failed to download file: %w", err)
	}

	// 返回清理函数
	cleanup := func() {
		if err := os.Remove(tempPath); err != nil {
			dm.logger.WithError(err).WithField("temp_path", tempPath).Warn("Failed to cleanup temp file")
		} else {
			dm.logger.WithField("temp_path", tempPath).Info("Temp file cleaned up successfully")
		}
	}

	dm.logger.WithFields(map[string]interface{}{
		"bucket":    fileInfo.OSS.Bucket,
		"key":       fileInfo.OSS.File,
		"temp_path": tempPath,
	}).Info("File downloaded to temp location")

	return tempPath, cleanup, nil
}

// DownloadMemoryDumpToPersistent 下载内存转储文件到持久化目录
func (dm *DynamicManager) DownloadMemoryDumpToPersistent(ctx context.Context, fileInfo *models.FileInfo, targetDir string) (string, error) {
	// 创建 MinIO 提供者
	provider, err := dm.createMinIOProvider(fileInfo.DownloadOption.OSS)
	if err != nil {
		return "", fmt.Errorf("failed to create MinIO provider: %w", err)
	}

	// 确保目标目录存在
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create target directory: %w", err)
	}

	// 获取文件流
	stream, err := provider.GetFileStream(ctx, fileInfo.OSS.Bucket, fileInfo.OSS.File)
	if err != nil {
		return "", err
	}
	defer stream.Close()

	// 生成本地文件名（基于原始文件名）
	fileName := filepath.Base(fileInfo.OSS.File)
	if fileName == "" || fileName == "." {
		// 如果无法从路径提取文件名，使用默认命名
		fileName = fmt.Sprintf("memory_dump_%s_%d.bin", fileInfo.OSS.Bucket, time.Now().Unix())
	}

	localPath := filepath.Join(targetDir, fileName)

	// 创建本地文件
	localFile, err := os.Create(localPath)
	if err != nil {
		return "", fmt.Errorf("failed to create local file: %w", err)
	}
	defer localFile.Close()

	// 流式复制数据
	_, err = io.Copy(localFile, stream)
	if err != nil {
		os.Remove(localPath) // 清理失败的文件
		return "", fmt.Errorf("failed to download file: %w", err)
	}

	dm.logger.WithFields(map[string]interface{}{
		"bucket":     fileInfo.OSS.Bucket,
		"key":        fileInfo.OSS.File,
		"local_path": localPath,
	}).Info("Memory dump downloaded to persistent location")

	return localPath, nil
}

// UploadFile 上传文件
func (dm *DynamicManager) UploadFile(ctx context.Context, localPath string, reader io.Reader, size int64, uploadOption *models.UploadOption, fileName string) (string, error) {
	// 创建 MinIO 提供者
	provider, err := dm.createMinIOProvider(uploadOption.OSS)
	if err != nil {
		return "", fmt.Errorf("failed to create MinIO provider: %w", err)
	}

	// 构建上传路径
	objectKey := fmt.Sprintf("%s/%s", uploadOption.Path, fileName)

	// 上传选项
	options := &UploadOptions{
		ContentType: "application/octet-stream",
		Metadata: map[string]string{
			"source": "memory-analysis",
		},
	}

	// 上传文件
	return provider.UploadFile(ctx, uploadOption.Bucket, objectKey, reader, size, options)
}

// DownloadSymbols 下载符号文件
func (dm *DynamicManager) DownloadSymbols(ctx context.Context, symbolOption *models.SymbolOption) (string, error) {
	// 创建 MinIO 提供者
	provider, err := dm.createMinIOProvider(symbolOption.OSS)
	if err != nil {
		return "", fmt.Errorf("failed to create MinIO provider: %w", err)
	}

	// 下载符号文件
	return provider.DownloadFile(ctx, symbolOption.Bucket, symbolOption.FilePath)
}

// DownloadSymbolsToPersistent 下载符号文件到持久化目录
func (dm *DynamicManager) DownloadSymbolsToPersistent(ctx context.Context, symbolOption *models.SymbolOption, targetDir string) (string, error) {
	// 创建 MinIO 提供者
	provider, err := dm.createMinIOProvider(symbolOption.OSS)
	if err != nil {
		return "", fmt.Errorf("failed to create MinIO provider: %w", err)
	}

	// 确保目标目录存在
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create target directory: %w", err)
	}

	// 获取文件流
	stream, err := provider.GetFileStream(ctx, symbolOption.Bucket, symbolOption.FilePath)
	if err != nil {
		return "", err
	}
	defer stream.Close()

	// 生成本地文件名（基于原始文件名）
	fileName := filepath.Base(symbolOption.FilePath)
	if fileName == "" || fileName == "." {
		// 如果无法从路径提取文件名，使用默认命名
		fileName = fmt.Sprintf("symbols_%s_%d.zip", symbolOption.Bucket, time.Now().Unix())
	}

	localPath := filepath.Join(targetDir, fileName)

	// 创建本地文件
	localFile, err := os.Create(localPath)
	if err != nil {
		return "", fmt.Errorf("failed to create local file: %w", err)
	}
	defer localFile.Close()

	// 流式复制数据
	_, err = io.Copy(localFile, stream)
	if err != nil {
		os.Remove(localPath) // 清理失败的文件
		return "", fmt.Errorf("failed to download file: %w", err)
	}

	dm.logger.WithFields(map[string]interface{}{
		"bucket":     symbolOption.Bucket,
		"key":        symbolOption.FilePath,
		"local_path": localPath,
	}).Info("Symbol file downloaded to persistent location")

	return localPath, nil
}

// Close 关闭管理器
func (dm *DynamicManager) Close() error {
	// 动态管理器不需要关闭操作
	return nil
}
