package engine

import (
	"fmt"
	"sync"
	"time"

	"memory-go/internal/models"
	"memory-go/pkg/logger"
)

// LoadBalancer 负载均衡器，用于在多个工作节点之间分发任务
type LoadBalancer struct {
	workers    []*WorkerNode
	workerMux  sync.RWMutex
	strategy   LoadBalanceStrategy
	logger     *logger.Logger
	roundRobin int
}

// WorkerNode 工作节点
type WorkerNode struct {
	ID             string
	MaxConcurrency int
	CurrentLoad    int
	TotalProcessed int64
	LastActiveTime time.Time
	IsHealthy      bool
	AverageLatency time.Duration
	mutex          sync.RWMutex
}

// LoadBalanceStrategy 负载均衡策略
type LoadBalanceStrategy int

const (
	RoundRobin LoadBalanceStrategy = iota
	LeastConnections
	WeightedRoundRobin
	ResourceBased
)

// NewLoadBalancer 创建新的负载均衡器
func NewLoadBalancer(strategy LoadBalanceStrategy, logger *logger.Logger) *LoadBalancer {
	return &LoadBalancer{
		workers:  make([]*WorkerNode, 0),
		strategy: strategy,
		logger:   logger,
	}
}

// AddWorker 添加工作节点
func (lb *LoadBalancer) AddWorker(workerID string, maxConcurrency int) {
	lb.workerMux.Lock()
	defer lb.workerMux.Unlock()

	worker := &WorkerNode{
		ID:             workerID,
		MaxConcurrency: maxConcurrency,
		CurrentLoad:    0,
		IsHealthy:      true,
		LastActiveTime: time.Now(),
	}

	lb.workers = append(lb.workers, worker)
	lb.logger.WithFields(map[string]interface{}{
		"worker_id":       workerID,
		"max_concurrency": maxConcurrency,
	}).Info("Worker added to load balancer")
}

// RemoveWorker 移除工作节点
func (lb *LoadBalancer) RemoveWorker(workerID string) error {
	lb.workerMux.Lock()
	defer lb.workerMux.Unlock()

	for i, worker := range lb.workers {
		if worker.ID == workerID {
			// 移除工作节点
			lb.workers = append(lb.workers[:i], lb.workers[i+1:]...)
			lb.logger.WithField("worker_id", workerID).Info("Worker removed from load balancer")
			return nil
		}
	}

	return fmt.Errorf("worker %s not found", workerID)
}

// SelectWorker 根据负载均衡策略选择工作节点
func (lb *LoadBalancer) SelectWorker() (*WorkerNode, error) {
	lb.workerMux.RLock()
	defer lb.workerMux.RUnlock()

	if len(lb.workers) == 0 {
		return nil, fmt.Errorf("no workers available")
	}

	switch lb.strategy {
	case RoundRobin:
		return lb.selectRoundRobin(), nil
	case LeastConnections:
		return lb.selectLeastConnections(), nil
	case WeightedRoundRobin:
		return lb.selectWeightedRoundRobin(), nil
	case ResourceBased:
		return lb.selectResourceBased(), nil
	default:
		return lb.selectRoundRobin(), nil
	}
}

// selectRoundRobin 轮询选择
func (lb *LoadBalancer) selectRoundRobin() *WorkerNode {
	healthyWorkers := lb.getHealthyWorkers()
	if len(healthyWorkers) == 0 {
		return nil
	}

	worker := healthyWorkers[lb.roundRobin%len(healthyWorkers)]
	lb.roundRobin++
	return worker
}

// selectLeastConnections 最少连接选择
func (lb *LoadBalancer) selectLeastConnections() *WorkerNode {
	healthyWorkers := lb.getHealthyWorkers()
	if len(healthyWorkers) == 0 {
		return nil
	}

	var selectedWorker *WorkerNode
	minLoad := int(^uint(0) >> 1) // 最大整数

	for _, worker := range healthyWorkers {
		worker.mutex.RLock()
		currentLoad := worker.CurrentLoad
		worker.mutex.RUnlock()

		if currentLoad < minLoad {
			minLoad = currentLoad
			selectedWorker = worker
		}
	}

	return selectedWorker
}

// selectWeightedRoundRobin 加权轮询选择
func (lb *LoadBalancer) selectWeightedRoundRobin() *WorkerNode {
	healthyWorkers := lb.getHealthyWorkers()
	if len(healthyWorkers) == 0 {
		return nil
	}

	// 基于最大并发数作为权重
	var totalWeight int
	for _, worker := range healthyWorkers {
		totalWeight += worker.MaxConcurrency
	}

	if totalWeight == 0 {
		return lb.selectRoundRobin()
	}

	target := lb.roundRobin % totalWeight
	lb.roundRobin++

	currentWeight := 0
	for _, worker := range healthyWorkers {
		currentWeight += worker.MaxConcurrency
		if target < currentWeight {
			return worker
		}
	}

	return healthyWorkers[0]
}

// selectResourceBased 基于资源的选择
func (lb *LoadBalancer) selectResourceBased() *WorkerNode {
	healthyWorkers := lb.getHealthyWorkers()
	if len(healthyWorkers) == 0 {
		return nil
	}

	var selectedWorker *WorkerNode
	bestScore := float64(-1)

	for _, worker := range healthyWorkers {
		worker.mutex.RLock()
		loadRatio := float64(worker.CurrentLoad) / float64(worker.MaxConcurrency)
		latencyPenalty := float64(worker.AverageLatency.Milliseconds()) / 1000.0
		worker.mutex.RUnlock()

		// 计算综合得分（越低越好）
		score := loadRatio + latencyPenalty*0.1

		if bestScore < 0 || score < bestScore {
			bestScore = score
			selectedWorker = worker
		}
	}

	return selectedWorker
}

// getHealthyWorkers 获取健康的工作节点
func (lb *LoadBalancer) getHealthyWorkers() []*WorkerNode {
	var healthyWorkers []*WorkerNode
	for _, worker := range lb.workers {
		worker.mutex.RLock()
		isHealthy := worker.IsHealthy && worker.CurrentLoad < worker.MaxConcurrency
		worker.mutex.RUnlock()

		if isHealthy {
			healthyWorkers = append(healthyWorkers, worker)
		}
	}
	return healthyWorkers
}

// AssignTask 分配任务到工作节点
func (lb *LoadBalancer) AssignTask(task *models.Task) (*WorkerNode, error) {
	worker, err := lb.SelectWorker()
	if err != nil {
		return nil, err
	}

	if worker == nil {
		return nil, fmt.Errorf("no available worker")
	}

	// 增加工作节点负载
	worker.mutex.Lock()
	worker.CurrentLoad++
	worker.LastActiveTime = time.Now()
	worker.mutex.Unlock()

	lb.logger.WithFields(map[string]interface{}{
		"worker_id": worker.ID,
		"task_id":   task.ID,
		"flow_id":   task.FlowID,
		"load":      worker.CurrentLoad,
	}).Info("Task assigned to worker")

	return worker, nil
}

// CompleteTask 完成任务，减少工作节点负载
func (lb *LoadBalancer) CompleteTask(workerID string, task *models.Task, duration time.Duration) {
	lb.workerMux.RLock()
	defer lb.workerMux.RUnlock()

	for _, worker := range lb.workers {
		if worker.ID == workerID {
			worker.mutex.Lock()
			worker.CurrentLoad--
			if worker.CurrentLoad < 0 {
				worker.CurrentLoad = 0
			}
			worker.TotalProcessed++
			worker.LastActiveTime = time.Now()

			// 更新平均延迟（简单的移动平均）
			if worker.AverageLatency == 0 {
				worker.AverageLatency = duration
			} else {
				worker.AverageLatency = (worker.AverageLatency + duration) / 2
			}
			worker.mutex.Unlock()

			lb.logger.WithFields(map[string]interface{}{
				"worker_id":       workerID,
				"task_id":         task.ID,
				"flow_id":         task.FlowID,
				"duration":        duration,
				"remaining_load":  worker.CurrentLoad,
				"total_processed": worker.TotalProcessed,
			}).Info("Task completed on worker")
			return
		}
	}
}

// UpdateWorkerHealth 更新工作节点健康状态
func (lb *LoadBalancer) UpdateWorkerHealth(workerID string, isHealthy bool) {
	lb.workerMux.RLock()
	defer lb.workerMux.RUnlock()

	for _, worker := range lb.workers {
		if worker.ID == workerID {
			worker.mutex.Lock()
			oldHealth := worker.IsHealthy
			worker.IsHealthy = isHealthy
			worker.mutex.Unlock()

			if oldHealth != isHealthy {
				lb.logger.WithFields(map[string]interface{}{
					"worker_id":  workerID,
					"is_healthy": isHealthy,
				}).Info("Worker health status updated")
			}
			return
		}
	}
}

// GetWorkerStats 获取工作节点统计信息
func (lb *LoadBalancer) GetWorkerStats() []map[string]interface{} {
	lb.workerMux.RLock()
	defer lb.workerMux.RUnlock()

	stats := make([]map[string]interface{}, len(lb.workers))
	for i, worker := range lb.workers {
		worker.mutex.RLock()
		stats[i] = map[string]interface{}{
			"worker_id":        worker.ID,
			"max_concurrency":  worker.MaxConcurrency,
			"current_load":     worker.CurrentLoad,
			"total_processed":  worker.TotalProcessed,
			"is_healthy":       worker.IsHealthy,
			"last_active_time": worker.LastActiveTime,
			"average_latency":  worker.AverageLatency.Milliseconds(),
			"utilization":      float64(worker.CurrentLoad) / float64(worker.MaxConcurrency) * 100,
		}
		worker.mutex.RUnlock()
	}

	return stats
}

// GetLoadBalancerStats 获取负载均衡器统计信息
func (lb *LoadBalancer) GetLoadBalancerStats() map[string]interface{} {
	lb.workerMux.RLock()
	defer lb.workerMux.RUnlock()

	totalWorkers := len(lb.workers)
	healthyWorkers := len(lb.getHealthyWorkers())
	totalCapacity := 0
	totalLoad := 0

	for _, worker := range lb.workers {
		worker.mutex.RLock()
		totalCapacity += worker.MaxConcurrency
		totalLoad += worker.CurrentLoad
		worker.mutex.RUnlock()
	}

	utilizationRate := 0.0
	if totalCapacity > 0 {
		utilizationRate = float64(totalLoad) / float64(totalCapacity) * 100
	}

	return map[string]interface{}{
		"strategy":         lb.getStrategyName(),
		"total_workers":    totalWorkers,
		"healthy_workers":  healthyWorkers,
		"total_capacity":   totalCapacity,
		"total_load":       totalLoad,
		"utilization_rate": utilizationRate,
		"available_slots":  totalCapacity - totalLoad,
	}
}

// getStrategyName 获取策略名称
func (lb *LoadBalancer) getStrategyName() string {
	switch lb.strategy {
	case RoundRobin:
		return "round_robin"
	case LeastConnections:
		return "least_connections"
	case WeightedRoundRobin:
		return "weighted_round_robin"
	case ResourceBased:
		return "resource_based"
	default:
		return "unknown"
	}
}

// SetStrategy 设置负载均衡策略
func (lb *LoadBalancer) SetStrategy(strategy LoadBalanceStrategy) {
	lb.workerMux.Lock()
	defer lb.workerMux.Unlock()

	oldStrategy := lb.strategy
	lb.strategy = strategy
	lb.roundRobin = 0 // 重置轮询计数器

	lb.logger.WithFields(map[string]interface{}{
		"old_strategy": lb.getStrategyNameByValue(oldStrategy),
		"new_strategy": lb.getStrategyName(),
	}).Info("Load balancing strategy updated")
}

// getStrategyNameByValue 根据值获取策略名称
func (lb *LoadBalancer) getStrategyNameByValue(strategy LoadBalanceStrategy) string {
	switch strategy {
	case RoundRobin:
		return "round_robin"
	case LeastConnections:
		return "least_connections"
	case WeightedRoundRobin:
		return "weighted_round_robin"
	case ResourceBased:
		return "resource_based"
	default:
		return "unknown"
	}
}
