package engine

import (
	"context"
	"fmt"
	"sync"
	"time"

	"memory-go/internal/models"
	"memory-go/pkg/logger"
)

// TaskScheduler 任务调度器，负责管理任务队列和并发控制
type TaskScheduler struct {
	maxConcurrentTasks int
	taskQueue          chan *models.Task
	runningTasks       map[string]*models.Task
	runningMutex       sync.RWMutex
	logger             *logger.Logger
	ctx                context.Context
	cancel             context.CancelFunc
	wg                 sync.WaitGroup
}

// NewTaskScheduler 创建新的任务调度器
func NewTaskScheduler(maxConcurrentTasks int, queueSize int, logger *logger.Logger) *TaskScheduler {
	ctx, cancel := context.WithCancel(context.Background())

	scheduler := &TaskScheduler{
		maxConcurrentTasks: maxConcurrentTasks,
		taskQueue:          make(chan *models.Task, queueSize),
		runningTasks:       make(map[string]*models.Task),
		logger:             logger,
		ctx:                ctx,
		cancel:             cancel,
	}

	// 启动工作协程
	for i := 0; i < maxConcurrentTasks; i++ {
		scheduler.wg.Add(1)
		go scheduler.worker(i)
	}

	return scheduler
}

// SubmitTask 提交任务到队列
func (ts *TaskScheduler) SubmitTask(task *models.Task) error {
	select {
	case ts.taskQueue <- task:
		ts.logger.WithFields(map[string]interface{}{
			"flow_id": task.FlowID,
			"task_id": task.ID,
		}).Info("Task submitted to queue")
		return nil
	case <-ts.ctx.Done():
		return fmt.Errorf("scheduler is shutting down")
	default:
		return fmt.Errorf("task queue is full")
	}
}

// worker 工作协程
func (ts *TaskScheduler) worker(workerID int) {
	defer ts.wg.Done()

	ts.logger.WithField("worker_id", workerID).Info("Task worker started")

	for {
		select {
		case task := <-ts.taskQueue:
			if task == nil {
				ts.logger.WithField("worker_id", workerID).Info("Task worker received nil task, shutting down")
				return
			}

			ts.logger.WithFields(map[string]interface{}{
				"worker_id": workerID,
				"flow_id":   task.FlowID,
				"task_id":   task.ID,
			}).Info("Worker processing task")

			ts.processTask(task)

		case <-ts.ctx.Done():
			ts.logger.WithField("worker_id", workerID).Info("Task worker shutting down")
			return
		}
	}
}

// processTask 处理单个任务
func (ts *TaskScheduler) processTask(task *models.Task) {
	// 将任务添加到运行中的任务列表
	ts.runningMutex.Lock()
	ts.runningTasks[task.FlowID] = task
	ts.runningMutex.Unlock()

	// 任务完成后从运行列表中移除
	defer func() {
		ts.runningMutex.Lock()
		delete(ts.runningTasks, task.FlowID)
		ts.runningMutex.Unlock()
	}()

	// 更新任务状态
	task.Status = models.TaskStatusRunning
	startedAt := time.Now()
	task.StartedAt = &startedAt

	ts.logger.WithFields(map[string]interface{}{
		"flow_id": task.FlowID,
		"task_id": task.ID,
	}).Info("Task execution started")

	// 这里应该调用实际的任务执行逻辑
	// 在实际实现中，这里会调用分析引擎的执行方法
	// 为了演示，我们模拟一个任务执行
	time.Sleep(100 * time.Millisecond) // 模拟任务执行时间

	// 更新任务完成状态
	completedAt := time.Now()
	task.CompletedAt = &completedAt
	task.Status = models.TaskStatusCompleted
	task.Progress = 100.0

	ts.logger.WithFields(map[string]interface{}{
		"flow_id":  task.FlowID,
		"task_id":  task.ID,
		"duration": completedAt.Sub(startedAt),
	}).Info("Task execution completed")
}

// GetRunningTasks 获取正在运行的任务
func (ts *TaskScheduler) GetRunningTasks() []*models.Task {
	ts.runningMutex.RLock()
	defer ts.runningMutex.RUnlock()

	tasks := make([]*models.Task, 0, len(ts.runningTasks))
	for _, task := range ts.runningTasks {
		tasks = append(tasks, task)
	}

	return tasks
}

// GetRunningTaskCount 获取正在运行的任务数量
func (ts *TaskScheduler) GetRunningTaskCount() int {
	ts.runningMutex.RLock()
	defer ts.runningMutex.RUnlock()
	return len(ts.runningTasks)
}

// GetQueueLength 获取队列长度
func (ts *TaskScheduler) GetQueueLength() int {
	return len(ts.taskQueue)
}

// GetMaxConcurrentTasks 获取最大并发任务数
func (ts *TaskScheduler) GetMaxConcurrentTasks() int {
	return ts.maxConcurrentTasks
}

// IsTaskRunning 检查任务是否正在运行
func (ts *TaskScheduler) IsTaskRunning(flowID string) bool {
	ts.runningMutex.RLock()
	defer ts.runningMutex.RUnlock()
	_, exists := ts.runningTasks[flowID]
	return exists
}

// CancelRunningTask 取消正在运行的任务
func (ts *TaskScheduler) CancelRunningTask(flowID string) error {
	ts.runningMutex.Lock()
	defer ts.runningMutex.Unlock()

	task, exists := ts.runningTasks[flowID]
	if !exists {
		return fmt.Errorf("task %s is not running", flowID)
	}

	// 更新任务状态
	task.Status = models.TaskStatusCancelled
	cancelledAt := time.Now()
	task.CompletedAt = &cancelledAt
	task.Error = "Task cancelled by user request"

	// 从运行列表中移除
	delete(ts.runningTasks, flowID)

	ts.logger.WithField("flow_id", flowID).Info("Running task cancelled")
	return nil
}

// GetSchedulerStats 获取调度器统计信息
func (ts *TaskScheduler) GetSchedulerStats() map[string]interface{} {
	ts.runningMutex.RLock()
	defer ts.runningMutex.RUnlock()

	return map[string]interface{}{
		"max_concurrent_tasks": ts.maxConcurrentTasks,
		"running_tasks":        len(ts.runningTasks),
		"queue_length":         len(ts.taskQueue),
		"queue_capacity":       cap(ts.taskQueue),
		"available_slots":      ts.maxConcurrentTasks - len(ts.runningTasks),
	}
}

// Shutdown 优雅关闭调度器
func (ts *TaskScheduler) Shutdown(timeout time.Duration) error {
	ts.logger.Info("Shutting down task scheduler")

	// 取消上下文，停止接收新任务
	ts.cancel()

	// 关闭任务队列
	close(ts.taskQueue)

	// 等待所有工作协程完成，带超时
	done := make(chan struct{})
	go func() {
		ts.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		ts.logger.Info("Task scheduler shutdown completed")
		return nil
	case <-time.After(timeout):
		ts.logger.Warn("Task scheduler shutdown timeout, some workers may still be running")
		return fmt.Errorf("shutdown timeout after %v", timeout)
	}
}

// SetMaxConcurrentTasks 动态设置最大并发任务数（需要重启调度器才能生效）
func (ts *TaskScheduler) SetMaxConcurrentTasks(maxTasks int) {
	ts.maxConcurrentTasks = maxTasks
	ts.logger.WithField("max_concurrent_tasks", maxTasks).Info("Max concurrent tasks updated")
}

// DrainQueue 排空队列中的待处理任务
func (ts *TaskScheduler) DrainQueue() []*models.Task {
	var drainedTasks []*models.Task

	for {
		select {
		case task := <-ts.taskQueue:
			if task != nil {
				task.Status = models.TaskStatusCancelled
				task.Error = "Task cancelled due to queue drain"
				drainedTasks = append(drainedTasks, task)
			}
		default:
			// 队列为空
			return drainedTasks
		}
	}
}

// PauseScheduler 暂停调度器（停止处理新任务，但不影响正在运行的任务）
func (ts *TaskScheduler) PauseScheduler() {
	// 实现暂停逻辑
	// 这里可以添加一个暂停标志，让 worker 暂停处理新任务
	ts.logger.Info("Task scheduler paused")
}

// ResumeScheduler 恢复调度器
func (ts *TaskScheduler) ResumeScheduler() {
	// 实现恢复逻辑
	ts.logger.Info("Task scheduler resumed")
}
