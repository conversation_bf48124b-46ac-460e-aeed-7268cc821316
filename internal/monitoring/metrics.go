package monitoring

import (
	"fmt"
	"sync"
	"time"
)

// MetricType 指标类型
type MetricType string

const (
	MetricTypeCounter   MetricType = "counter"
	MetricTypeGauge     MetricType = "gauge"
	MetricTypeHistogram MetricType = "histogram"
	MetricTypeSummary   MetricType = "summary"
)

// Metric 指标接口
type Metric interface {
	GetName() string
	GetType() MetricType
	GetValue() interface{}
	GetLabels() map[string]string
	GetTimestamp() time.Time
}

// Counter 计数器指标
type Counter struct {
	name      string
	value     int64
	labels    map[string]string
	timestamp time.Time
	mutex     sync.RWMutex
}

// NewCounter 创建新的计数器
func NewCounter(name string, labels map[string]string) *Counter {
	return &Counter{
		name:      name,
		value:     0,
		labels:    labels,
		timestamp: time.Now(),
	}
}

// Inc 增加计数器
func (c *Counter) Inc() {
	c.Add(1)
}

// Add 增加指定值
func (c *Counter) Add(value int64) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.value += value
	c.timestamp = time.Now()
}

// Get 获取当前值
func (c *Counter) Get() int64 {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.value
}

// Reset 重置计数器
func (c *Counter) Reset() {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.value = 0
	c.timestamp = time.Now()
}

// GetName 获取指标名称
func (c *Counter) GetName() string {
	return c.name
}

// GetType 获取指标类型
func (c *Counter) GetType() MetricType {
	return MetricTypeCounter
}

// GetValue 获取指标值
func (c *Counter) GetValue() interface{} {
	return c.Get()
}

// GetLabels 获取标签
func (c *Counter) GetLabels() map[string]string {
	return c.labels
}

// GetTimestamp 获取时间戳
func (c *Counter) GetTimestamp() time.Time {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.timestamp
}

// Gauge 仪表盘指标
type Gauge struct {
	name      string
	value     float64
	labels    map[string]string
	timestamp time.Time
	mutex     sync.RWMutex
}

// NewGauge 创建新的仪表盘指标
func NewGauge(name string, labels map[string]string) *Gauge {
	return &Gauge{
		name:      name,
		value:     0,
		labels:    labels,
		timestamp: time.Now(),
	}
}

// Set 设置值
func (g *Gauge) Set(value float64) {
	g.mutex.Lock()
	defer g.mutex.Unlock()
	g.value = value
	g.timestamp = time.Now()
}

// Inc 增加1
func (g *Gauge) Inc() {
	g.Add(1)
}

// Dec 减少1
func (g *Gauge) Dec() {
	g.Add(-1)
}

// Add 增加指定值
func (g *Gauge) Add(value float64) {
	g.mutex.Lock()
	defer g.mutex.Unlock()
	g.value += value
	g.timestamp = time.Now()
}

// Get 获取当前值
func (g *Gauge) Get() float64 {
	g.mutex.RLock()
	defer g.mutex.RUnlock()
	return g.value
}

// GetName 获取指标名称
func (g *Gauge) GetName() string {
	return g.name
}

// GetType 获取指标类型
func (g *Gauge) GetType() MetricType {
	return MetricTypeGauge
}

// GetValue 获取指标值
func (g *Gauge) GetValue() interface{} {
	return g.Get()
}

// GetLabels 获取标签
func (g *Gauge) GetLabels() map[string]string {
	return g.labels
}

// GetTimestamp 获取时间戳
func (g *Gauge) GetTimestamp() time.Time {
	g.mutex.RLock()
	defer g.mutex.RUnlock()
	return g.timestamp
}

// Histogram 直方图指标
type Histogram struct {
	name      string
	buckets   []float64
	counts    []int64
	sum       float64
	count     int64
	labels    map[string]string
	timestamp time.Time
	mutex     sync.RWMutex
}

// NewHistogram 创建新的直方图指标
func NewHistogram(name string, buckets []float64, labels map[string]string) *Histogram {
	return &Histogram{
		name:      name,
		buckets:   buckets,
		counts:    make([]int64, len(buckets)+1), // +1 for +Inf bucket
		labels:    labels,
		timestamp: time.Now(),
	}
}

// Observe 观察一个值
func (h *Histogram) Observe(value float64) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.sum += value
	h.count++
	h.timestamp = time.Now()

	// 找到对应的桶
	for i, bucket := range h.buckets {
		if value <= bucket {
			h.counts[i]++
		}
	}
	// +Inf bucket
	h.counts[len(h.buckets)]++
}

// GetBuckets 获取桶信息
func (h *Histogram) GetBuckets() map[string]int64 {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	buckets := make(map[string]int64)
	for i, bucket := range h.buckets {
		buckets[fmt.Sprintf("%.2f", bucket)] = h.counts[i]
	}
	buckets["+Inf"] = h.counts[len(h.buckets)]
	return buckets
}

// GetSum 获取总和
func (h *Histogram) GetSum() float64 {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	return h.sum
}

// GetCount 获取总数
func (h *Histogram) GetCount() int64 {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	return h.count
}

// GetName 获取指标名称
func (h *Histogram) GetName() string {
	return h.name
}

// GetType 获取指标类型
func (h *Histogram) GetType() MetricType {
	return MetricTypeHistogram
}

// GetValue 获取指标值
func (h *Histogram) GetValue() interface{} {
	return map[string]interface{}{
		"buckets": h.GetBuckets(),
		"sum":     h.GetSum(),
		"count":   h.GetCount(),
	}
}

// GetLabels 获取标签
func (h *Histogram) GetLabels() map[string]string {
	return h.labels
}

// GetTimestamp 获取时间戳
func (h *Histogram) GetTimestamp() time.Time {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	return h.timestamp
}

// MetricsRegistry 指标注册表
type MetricsRegistry struct {
	metrics map[string]Metric
	mutex   sync.RWMutex
}

// NewMetricsRegistry 创建新的指标注册表
func NewMetricsRegistry() *MetricsRegistry {
	return &MetricsRegistry{
		metrics: make(map[string]Metric),
	}
}

// Register 注册指标
func (r *MetricsRegistry) Register(metric Metric) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.metrics[metric.GetName()] = metric
}

// Unregister 取消注册指标
func (r *MetricsRegistry) Unregister(name string) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	delete(r.metrics, name)
}

// Get 获取指标
func (r *MetricsRegistry) Get(name string) (Metric, bool) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	metric, exists := r.metrics[name]
	return metric, exists
}

// GetAll 获取所有指标
func (r *MetricsRegistry) GetAll() map[string]Metric {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	result := make(map[string]Metric)
	for name, metric := range r.metrics {
		result[name] = metric
	}
	return result
}

// GetByType 根据类型获取指标
func (r *MetricsRegistry) GetByType(metricType MetricType) []Metric {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var result []Metric
	for _, metric := range r.metrics {
		if metric.GetType() == metricType {
			result = append(result, metric)
		}
	}
	return result
}

// ToMap 转换为 map 格式
func (r *MetricsRegistry) ToMap() map[string]interface{} {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	result := make(map[string]interface{})
	for name, metric := range r.metrics {
		result[name] = map[string]interface{}{
			"type":      metric.GetType(),
			"value":     metric.GetValue(),
			"labels":    metric.GetLabels(),
			"timestamp": metric.GetTimestamp().Format(time.RFC3339),
		}
	}
	return result
}

// 全局指标注册表
var GlobalRegistry = NewMetricsRegistry()

// 便捷函数
func RegisterCounter(name string, labels map[string]string) *Counter {
	counter := NewCounter(name, labels)
	GlobalRegistry.Register(counter)
	return counter
}

func RegisterGauge(name string, labels map[string]string) *Gauge {
	gauge := NewGauge(name, labels)
	GlobalRegistry.Register(gauge)
	return gauge
}

func RegisterHistogram(name string, buckets []float64, labels map[string]string) *Histogram {
	histogram := NewHistogram(name, buckets, labels)
	GlobalRegistry.Register(histogram)
	return histogram
}
