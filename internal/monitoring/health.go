package monitoring

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// HealthStatus 健康状态
type HealthStatus string

const (
	HealthStatusHealthy   HealthStatus = "healthy"
	HealthStatusUnhealthy HealthStatus = "unhealthy"
	HealthStatusDegraded  HealthStatus = "degraded"
	HealthStatusUnknown   HealthStatus = "unknown"
)

// HealthCheck 健康检查接口
type HealthCheck interface {
	Name() string
	Check(ctx context.Context) HealthCheckResult
}

// HealthCheckResult 健康检查结果
type HealthCheckResult struct {
	Status    HealthStatus           `json:"status"`
	Message   string                 `json:"message,omitempty"`
	Details   map[string]interface{} `json:"details,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	Duration  time.Duration          `json:"duration"`
}

// HealthChecker 健康检查器
type HealthChecker struct {
	checks  map[string]HealthCheck
	results map[string]HealthCheckResult
	timeout time.Duration
	mutex   sync.RWMutex
}

// NewHealthChecker 创建新的健康检查器
func NewHealthChecker(timeout time.Duration) *HealthChecker {
	return &HealthChecker{
		checks:  make(map[string]HealthCheck),
		results: make(map[string]HealthCheckResult),
		timeout: timeout,
	}
}

// Register 注册健康检查
func (hc *HealthChecker) Register(check HealthCheck) {
	hc.mutex.Lock()
	defer hc.mutex.Unlock()
	hc.checks[check.Name()] = check
}

// Unregister 取消注册健康检查
func (hc *HealthChecker) Unregister(name string) {
	hc.mutex.Lock()
	defer hc.mutex.Unlock()
	delete(hc.checks, name)
	delete(hc.results, name)
}

// CheckAll 执行所有健康检查
func (hc *HealthChecker) CheckAll(ctx context.Context) map[string]HealthCheckResult {
	hc.mutex.RLock()
	checks := make(map[string]HealthCheck)
	for name, check := range hc.checks {
		checks[name] = check
	}
	hc.mutex.RUnlock()

	results := make(map[string]HealthCheckResult)
	var wg sync.WaitGroup

	for name, check := range checks {
		wg.Add(1)
		go func(name string, check HealthCheck) {
			defer wg.Done()

			// 创建带超时的上下文
			checkCtx, cancel := context.WithTimeout(ctx, hc.timeout)
			defer cancel()

			start := time.Now()
			result := check.Check(checkCtx)
			result.Duration = time.Since(start)
			result.Timestamp = time.Now()

			results[name] = result
		}(name, check)
	}

	wg.Wait()

	// 更新缓存的结果
	hc.mutex.Lock()
	for name, result := range results {
		hc.results[name] = result
	}
	hc.mutex.Unlock()

	return results
}

// CheckOne 执行单个健康检查
func (hc *HealthChecker) CheckOne(ctx context.Context, name string) (HealthCheckResult, error) {
	hc.mutex.RLock()
	check, exists := hc.checks[name]
	hc.mutex.RUnlock()

	if !exists {
		return HealthCheckResult{}, fmt.Errorf("health check '%s' not found", name)
	}

	checkCtx, cancel := context.WithTimeout(ctx, hc.timeout)
	defer cancel()

	start := time.Now()
	result := check.Check(checkCtx)
	result.Duration = time.Since(start)
	result.Timestamp = time.Now()

	// 更新缓存的结果
	hc.mutex.Lock()
	hc.results[name] = result
	hc.mutex.Unlock()

	return result, nil
}

// GetOverallStatus 获取整体健康状态
func (hc *HealthChecker) GetOverallStatus() HealthStatus {
	hc.mutex.RLock()
	defer hc.mutex.RUnlock()

	if len(hc.results) == 0 {
		return HealthStatusUnknown
	}

	hasUnhealthy := false
	hasDegraded := false

	for _, result := range hc.results {
		switch result.Status {
		case HealthStatusUnhealthy:
			hasUnhealthy = true
		case HealthStatusDegraded:
			hasDegraded = true
		}
	}

	if hasUnhealthy {
		return HealthStatusUnhealthy
	}
	if hasDegraded {
		return HealthStatusDegraded
	}
	return HealthStatusHealthy
}

// GetResults 获取所有健康检查结果
func (hc *HealthChecker) GetResults() map[string]HealthCheckResult {
	hc.mutex.RLock()
	defer hc.mutex.RUnlock()

	results := make(map[string]HealthCheckResult)
	for name, result := range hc.results {
		results[name] = result
	}
	return results
}

// 预定义的健康检查实现

// DatabaseHealthCheck 数据库健康检查
type DatabaseHealthCheck struct {
	name string
	ping func(ctx context.Context) error
}

// NewDatabaseHealthCheck 创建数据库健康检查
func NewDatabaseHealthCheck(name string, ping func(ctx context.Context) error) *DatabaseHealthCheck {
	return &DatabaseHealthCheck{
		name: name,
		ping: ping,
	}
}

// Name 返回检查名称
func (dhc *DatabaseHealthCheck) Name() string {
	return dhc.name
}

// Check 执行健康检查
func (dhc *DatabaseHealthCheck) Check(ctx context.Context) HealthCheckResult {
	err := dhc.ping(ctx)
	if err != nil {
		return HealthCheckResult{
			Status:  HealthStatusUnhealthy,
			Message: fmt.Sprintf("Database connection failed: %v", err),
		}
	}
	return HealthCheckResult{
		Status:  HealthStatusHealthy,
		Message: "Database connection successful",
	}
}

// HTTPHealthCheck HTTP 服务健康检查
type HTTPHealthCheck struct {
	name string
	url  string
	ping func(ctx context.Context, url string) error
}

// NewHTTPHealthCheck 创建 HTTP 健康检查
func NewHTTPHealthCheck(name, url string, ping func(ctx context.Context, url string) error) *HTTPHealthCheck {
	return &HTTPHealthCheck{
		name: name,
		url:  url,
		ping: ping,
	}
}

// Name 返回检查名称
func (hhc *HTTPHealthCheck) Name() string {
	return hhc.name
}

// Check 执行健康检查
func (hhc *HTTPHealthCheck) Check(ctx context.Context) HealthCheckResult {
	err := hhc.ping(ctx, hhc.url)
	if err != nil {
		return HealthCheckResult{
			Status:  HealthStatusUnhealthy,
			Message: fmt.Sprintf("HTTP service unavailable: %v", err),
			Details: map[string]interface{}{
				"url": hhc.url,
			},
		}
	}
	return HealthCheckResult{
		Status:  HealthStatusHealthy,
		Message: "HTTP service available",
		Details: map[string]interface{}{
			"url": hhc.url,
		},
	}
}

// DiskSpaceHealthCheck 磁盘空间健康检查
type DiskSpaceHealthCheck struct {
	name         string
	path         string
	threshold    float64 // 阈值百分比 (0-100)
	getDiskUsage func(path string) (float64, error)
}

// NewDiskSpaceHealthCheck 创建磁盘空间健康检查
func NewDiskSpaceHealthCheck(name, path string, threshold float64, getDiskUsage func(path string) (float64, error)) *DiskSpaceHealthCheck {
	return &DiskSpaceHealthCheck{
		name:         name,
		path:         path,
		threshold:    threshold,
		getDiskUsage: getDiskUsage,
	}
}

// Name 返回检查名称
func (dshc *DiskSpaceHealthCheck) Name() string {
	return dshc.name
}

// Check 执行健康检查
func (dshc *DiskSpaceHealthCheck) Check(ctx context.Context) HealthCheckResult {
	usage, err := dshc.getDiskUsage(dshc.path)
	if err != nil {
		return HealthCheckResult{
			Status:  HealthStatusUnhealthy,
			Message: fmt.Sprintf("Failed to get disk usage: %v", err),
			Details: map[string]interface{}{
				"path": dshc.path,
			},
		}
	}

	details := map[string]interface{}{
		"path":      dshc.path,
		"usage":     usage,
		"threshold": dshc.threshold,
	}

	if usage > dshc.threshold {
		return HealthCheckResult{
			Status:  HealthStatusUnhealthy,
			Message: fmt.Sprintf("Disk usage %.2f%% exceeds threshold %.2f%%", usage, dshc.threshold),
			Details: details,
		}
	}

	if usage > dshc.threshold*0.8 { // 80% of threshold
		return HealthCheckResult{
			Status:  HealthStatusDegraded,
			Message: fmt.Sprintf("Disk usage %.2f%% is approaching threshold %.2f%%", usage, dshc.threshold),
			Details: details,
		}
	}

	return HealthCheckResult{
		Status:  HealthStatusHealthy,
		Message: fmt.Sprintf("Disk usage %.2f%% is within acceptable limits", usage),
		Details: details,
	}
}

// MemoryHealthCheck 内存使用健康检查
type MemoryHealthCheck struct {
	name           string
	threshold      float64 // 阈值百分比 (0-100)
	getMemoryUsage func() (float64, error)
}

// NewMemoryHealthCheck 创建内存健康检查
func NewMemoryHealthCheck(name string, threshold float64, getMemoryUsage func() (float64, error)) *MemoryHealthCheck {
	return &MemoryHealthCheck{
		name:           name,
		threshold:      threshold,
		getMemoryUsage: getMemoryUsage,
	}
}

// Name 返回检查名称
func (mhc *MemoryHealthCheck) Name() string {
	return mhc.name
}

// Check 执行健康检查
func (mhc *MemoryHealthCheck) Check(ctx context.Context) HealthCheckResult {
	usage, err := mhc.getMemoryUsage()
	if err != nil {
		return HealthCheckResult{
			Status:  HealthStatusUnhealthy,
			Message: fmt.Sprintf("Failed to get memory usage: %v", err),
		}
	}

	details := map[string]interface{}{
		"usage":     usage,
		"threshold": mhc.threshold,
	}

	if usage > mhc.threshold {
		return HealthCheckResult{
			Status:  HealthStatusUnhealthy,
			Message: fmt.Sprintf("Memory usage %.2f%% exceeds threshold %.2f%%", usage, mhc.threshold),
			Details: details,
		}
	}

	if usage > mhc.threshold*0.8 { // 80% of threshold
		return HealthCheckResult{
			Status:  HealthStatusDegraded,
			Message: fmt.Sprintf("Memory usage %.2f%% is approaching threshold %.2f%%", usage, mhc.threshold),
			Details: details,
		}
	}

	return HealthCheckResult{
		Status:  HealthStatusHealthy,
		Message: fmt.Sprintf("Memory usage %.2f%% is within acceptable limits", usage),
		Details: details,
	}
}
