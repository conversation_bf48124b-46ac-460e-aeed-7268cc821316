package volatility

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"memory-go/internal/config"
	"memory-go/internal/models"
	"memory-go/pkg/logger"
)

// Executor Volatility 执行器
type Executor struct {
	config      *config.VolatilityConfig
	logger      *logger.Logger
	vol2Handler *Vol2Handler
	procMgr     *ProcessManager
}

// New 创建新的 Volatility 执行器
func New(cfg *config.VolatilityConfig, log *logger.Logger) *Executor {
	executor := &Executor{
		config:  cfg,
		logger:  log,
		procMgr: NewProcessManager(cfg.MaxWorkers, log),
	}

	// 创建 Vol2 处理器
	executor.vol2Handler = NewVol2Handler(executor)

	return executor
}

// ExecutePlugin 执行 Volatility 3 插件
func (e *Executor) ExecutePlugin(ctx context.Context, plugin, dumpPath, osType string, options map[string]interface{}) (*models.VolatilityResult, error) {
	startTime := time.Now()

	e.logger.WithFields(map[string]interface{}{
		"plugin":    plugin,
		"dump_path": dumpPath,
		"os_type":   osType,
	}).Info("Starting Volatility 3 plugin execution")

	// 构建命令
	cmd, err := e.buildVol3Command(plugin, dumpPath, osType, options)
	if err != nil {
		return nil, fmt.Errorf("failed to build vol3 command: %w", err)
	}

	// 执行命令
	result, err := e.runCommand(ctx, cmd)
	if err != nil {
		e.logger.WithError(err).WithField("plugin", plugin).Warn("Vol3 execution failed, will try vol2 fallback")
		return e.vol2Handler.executeVol2(ctx, plugin, dumpPath, osType, options)
	}

	// 解析结果
	volatilityResult, err := e.parseVol3Output(result, plugin)
	if err != nil {
		return nil, fmt.Errorf("failed to parse vol3 output: %w", err)
	}

	volatilityResult.Duration = time.Since(startTime)
	volatilityResult.Success = true

	e.logger.WithFields(map[string]interface{}{
		"plugin":   plugin,
		"duration": volatilityResult.Duration,
		"records":  len(volatilityResult.ProcessedData),
	}).Info("Volatility 3 plugin execution completed")

	return volatilityResult, nil
}

// ExecuteCustomPlugin 执行自定义插件
func (e *Executor) ExecuteCustomPlugin(ctx context.Context, pluginPath, dumpPath, osType string, options map[string]interface{}) (*models.VolatilityResult, error) {
	startTime := time.Now()

	e.logger.WithFields(map[string]interface{}{
		"plugin_path": pluginPath,
		"dump_path":   dumpPath,
		"os_type":     osType,
	}).Info("Starting custom plugin execution")

	// 下载自定义插件（如果需要）
	localPluginPath, err := e.downloadCustomPlugin(ctx, pluginPath)
	if err != nil {
		return nil, fmt.Errorf("failed to download custom plugin: %w", err)
	}
	defer func() {
		if localPluginPath != pluginPath {
			os.Remove(localPluginPath)
		}
	}()

	// 构建自定义插件命令
	cmd, err := e.buildCustomPluginCommand(localPluginPath, dumpPath, osType, options)
	if err != nil {
		return nil, fmt.Errorf("failed to build custom plugin command: %w", err)
	}

	// 执行命令
	result, err := e.runCommand(ctx, cmd)
	if err != nil {
		return nil, fmt.Errorf("custom plugin execution failed: %w", err)
	}

	// 解析结果
	volatilityResult, err := e.parseCustomPluginOutput(result, filepath.Base(pluginPath))
	if err != nil {
		return nil, fmt.Errorf("failed to parse custom plugin output: %w", err)
	}

	volatilityResult.Duration = time.Since(startTime)
	volatilityResult.Success = true

	e.logger.WithFields(map[string]interface{}{
		"plugin":   filepath.Base(pluginPath),
		"duration": volatilityResult.Duration,
		"records":  len(volatilityResult.ProcessedData),
	}).Info("Custom plugin execution completed")

	return volatilityResult, nil
}

// buildVol3Command 构建 Volatility 3 命令
func (e *Executor) buildVol3Command(plugin, dumpPath, osType string, options map[string]interface{}) (*exec.Cmd, error) {
	args := []string{}

	// 添加缓存路径（必须在其他参数之前）
	if e.config.CachePath != "" {
		args = append(args, "--cache-path", e.config.CachePath)
	}

	// 添加符号路径
	for _, symbolPath := range e.config.SymbolPaths {
		if _, err := os.Stat(symbolPath); err == nil {
			args = append(args, "--symbol-dirs", symbolPath)
		}
	}

	// 添加文件和插件参数
	args = append(args, "-f", dumpPath)
	args = append(args, "-r", "json")
	args = append(args, plugin)

	// 添加插件特定的参数
	pluginArgs, err := e.buildPluginArgs(plugin, options)
	if err != nil {
		return nil, err
	}
	args = append(args, pluginArgs...)

	cmd := exec.Command("python3", append([]string{e.config.Vol3Path}, args...)...)

	// 设置环境变量
	cmd.Env = os.Environ()
	cmd.Env = append(cmd.Env, "PYTHONPATH="+filepath.Dir(e.config.Vol3Path))

	e.logger.WithField("command", cmd.String()).Debug("Built vol3 command")
	return cmd, nil
}

// buildPluginArgs 构建插件特定的参数
func (e *Executor) buildPluginArgs(plugin string, options map[string]interface{}) ([]string, error) {
	var args []string

	// 根据插件类型添加特定参数
	switch {
	case strings.Contains(plugin, "pslist"):
		if pid, ok := options["pid"]; ok {
			args = append(args, "--pid", fmt.Sprintf("%v", pid))
		}
	case strings.Contains(plugin, "dlllist"):
		if pid, ok := options["pid"]; ok {
			args = append(args, "--pid", fmt.Sprintf("%v", pid))
		}
	case strings.Contains(plugin, "handles"):
		if pid, ok := options["pid"]; ok {
			args = append(args, "--pid", fmt.Sprintf("%v", pid))
		}
	case strings.Contains(plugin, "memmap"):
		if pid, ok := options["pid"]; ok {
			args = append(args, "--pid", fmt.Sprintf("%v", pid))
		}
		// 设置输出目录
		outputDir := filepath.Join(e.config.CachePath, "memmap_output")
		os.MkdirAll(outputDir, 0755)
		args = append(args, "--dump-dir", outputDir)
	case strings.Contains(plugin, "vadinfo"):
		if pid, ok := options["pid"]; ok {
			args = append(args, "--pid", fmt.Sprintf("%v", pid))
		}
	case strings.Contains(plugin, "filescan"):
		// filescan 通常不需要额外参数
	case strings.Contains(plugin, "netscan"):
		// netscan 通常不需要额外参数
	}

	return args, nil
}

// runCommand 执行命令
func (e *Executor) runCommand(ctx context.Context, cmd *exec.Cmd) ([]byte, error) {
	// 生成进程 ID
	processID := fmt.Sprintf("vol_%d", time.Now().UnixNano())

	// 设置超时
	timeout := time.Duration(e.config.Timeout) * time.Second

	// 使用进程管理器执行命令
	return e.procMgr.ExecuteCommand(ctx, cmd, processID, timeout)
}

// parseVol3Output 解析 Volatility 3 输出
func (e *Executor) parseVol3Output(output []byte, plugin string) (*models.VolatilityResult, error) {
	result := &models.VolatilityResult{
		Plugin:      plugin,
		RawData:     make([]map[string]interface{}, 0),
		FileOutputs: make([]models.FileOutput, 0),
	}

	// Vol3 输出包含进度信息和警告，需要提取JSON部分
	jsonData, err := e.extractJSONFromVol3Output(output)
	if err != nil {
		e.logger.WithError(err).WithField("plugin", plugin).Warn("Failed to extract JSON from vol3 output, falling back to text parsing")
		return e.parseTextOutput(output, plugin)
	}

	result.RawData = jsonData
	result.ProcessedData = jsonData // 暂时直接使用原始数据，后续会在结果处理器中进行字段映射

	// 检查是否有文件输出
	for _, record := range jsonData {
		if fileOutput, ok := record["file_output"]; ok && fileOutput != nil {
			result.FileOutputs = append(result.FileOutputs, models.FileOutput{
				LocalPath: fmt.Sprintf("%v", fileOutput),
			})
		}
	}

	return result, nil
}

// extractJSONFromVol3Output 从Vol3输出中提取JSON数据
func (e *Executor) extractJSONFromVol3Output(output []byte) ([]map[string]interface{}, error) {
	outputStr := string(output)
	lines := strings.Split(outputStr, "\n")

	// 查找JSON数组的开始和结束
	var jsonLines []string
	inJSON := false
	bracketCount := 0

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 跳过空行
		if line == "" {
			continue
		}

		// 跳过进度信息和警告
		if strings.Contains(line, "Progress:") ||
			strings.Contains(line, "WARNING") ||
			strings.Contains(line, "Volatility 3 Framework") ||
			strings.Contains(line, "Scanning") {
			continue
		}

		// 检查是否是JSON数组开始
		if strings.HasPrefix(line, "[") {
			inJSON = true
			bracketCount = strings.Count(line, "[") - strings.Count(line, "]")
			jsonLines = append(jsonLines, line)

			// 如果这一行就完成了JSON数组，直接结束
			if bracketCount == 0 {
				break
			}
			continue
		}

		// 如果在JSON中，继续收集行
		if inJSON {
			jsonLines = append(jsonLines, line)
			bracketCount += strings.Count(line, "[") - strings.Count(line, "]")

			// 如果括号平衡，JSON数组结束
			if bracketCount == 0 {
				break
			}
		}
	}

	if len(jsonLines) == 0 {
		return nil, fmt.Errorf("no JSON data found in vol3 output")
	}

	// 合并JSON行并解析
	jsonStr := strings.Join(jsonLines, "\n")
	var jsonData []map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &jsonData); err != nil {
		return nil, fmt.Errorf("failed to parse extracted JSON: %w", err)
	}

	return jsonData, nil
}

// parseTextOutput 解析文本输出（用于某些不支持 JSON 的插件）
func (e *Executor) parseTextOutput(output []byte, plugin string) (*models.VolatilityResult, error) {
	result := &models.VolatilityResult{
		Plugin:        plugin,
		RawData:       make([]map[string]interface{}, 0),
		ProcessedData: make([]map[string]interface{}, 0),
		FileOutputs:   make([]models.FileOutput, 0),
	}

	// 简单的文本解析逻辑
	lines := strings.Split(string(output), "\n")
	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 创建一个简单的记录
		record := map[string]interface{}{
			"line_number": i + 1,
			"content":     line,
		}

		result.RawData = append(result.RawData, record)
		result.ProcessedData = append(result.ProcessedData, record)
	}

	return result, nil
}

// executeVol2Plugin 执行 Volatility 2 插件（回退机制）
func (e *Executor) executeVol2Plugin(ctx context.Context, plugin, dumpPath, osType string, options map[string]interface{}) (*models.VolatilityResult, error) {
	e.logger.WithField("plugin", plugin).Info("Executing Volatility 2 fallback")

	// 构建 vol2 命令
	cmd, err := e.buildVol2Command(plugin, dumpPath, osType, options)
	if err != nil {
		return nil, fmt.Errorf("failed to build vol2 command: %w", err)
	}

	// 执行命令
	result, err := e.runCommand(ctx, cmd)
	if err != nil {
		return nil, fmt.Errorf("vol2 execution failed: %w", err)
	}

	// 解析 vol2 结果
	volatilityResult, err := e.parseVol2Output(result, plugin)
	if err != nil {
		return nil, fmt.Errorf("failed to parse vol2 output: %w", err)
	}

	volatilityResult.Success = true
	return volatilityResult, nil
}

// buildVol2Command 构建 Volatility 2 命令
func (e *Executor) buildVol2Command(plugin, dumpPath, osType string, options map[string]interface{}) (*exec.Cmd, error) {
	// 将 vol3 插件名转换为 vol2 插件名
	vol2Plugin := e.convertVol3ToVol2Plugin(plugin)

	args := []string{
		e.config.Vol2Path,
		"-f", dumpPath,
		vol2Plugin,
	}

	// 添加 profile（vol2 需要）
	profile, err := e.detectProfile(dumpPath, osType)
	if err != nil {
		e.logger.WithError(err).Warn("Failed to detect profile, using default")
		profile = e.getDefaultProfile(osType)
	}

	if profile != "" {
		args = append(args, "--profile", profile)
	}

	// 添加插件特定参数
	pluginArgs, err := e.buildVol2PluginArgs(vol2Plugin, options)
	if err != nil {
		return nil, err
	}
	args = append(args, pluginArgs...)

	cmd := exec.Command(e.config.PythonPath, args...)
	cmd.Env = os.Environ()

	e.logger.WithField("command", cmd.String()).Debug("Built vol2 command")
	return cmd, nil
}

// convertVol3ToVol2Plugin 将 vol3 插件名转换为 vol2 插件名
func (e *Executor) convertVol3ToVol2Plugin(vol3Plugin string) string {
	// 插件名映射表
	pluginMap := map[string]string{
		"windows.pslist.PsList":     "pslist",
		"windows.dlllist.DllList":   "dlllist",
		"windows.handles.Handles":   "handles",
		"windows.filescan.FileScan": "filescan",
		"windows.netscan.NetScan":   "netscan",
		"linux.pslist.PsList":       "linux_pslist",
		"linux.lsmod.Lsmod":         "linux_lsmod",
	}

	if vol2Plugin, exists := pluginMap[vol3Plugin]; exists {
		return vol2Plugin
	}

	// 如果没有映射，尝试提取插件名
	parts := strings.Split(vol3Plugin, ".")
	if len(parts) > 0 {
		return strings.ToLower(parts[len(parts)-1])
	}

	return vol3Plugin
}

// detectProfile 检测内存转储的 profile
func (e *Executor) detectProfile(dumpPath, osType string) (string, error) {
	// 使用 vol2 的 imageinfo 插件检测 profile
	cmd := exec.Command(e.config.PythonPath, e.config.Vol2Path, "-f", dumpPath, "imageinfo")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return "", err
	}

	// 解析 imageinfo 输出
	scanner := bufio.NewScanner(strings.NewReader(string(output)))
	for scanner.Scan() {
		line := scanner.Text()
		if strings.Contains(line, "Suggested Profile(s)") {
			parts := strings.Split(line, ":")
			if len(parts) > 1 {
				profiles := strings.TrimSpace(parts[1])
				// 取第一个建议的 profile
				if profiles != "" {
					firstProfile := strings.Split(profiles, ",")[0]
					return strings.TrimSpace(firstProfile), nil
				}
			}
		}
	}

	return "", fmt.Errorf("no profile detected")
}

// getDefaultProfile 获取默认 profile
func (e *Executor) getDefaultProfile(osType string) string {
	switch strings.ToLower(osType) {
	case "windows":
		return "Win10x64_19041"
	case "linux":
		return "LinuxUbuntu1804x64"
	default:
		return ""
	}
}

// buildVol2PluginArgs 构建 vol2 插件参数
func (e *Executor) buildVol2PluginArgs(plugin string, options map[string]interface{}) ([]string, error) {
	var args []string

	// 根据插件添加参数
	switch plugin {
	case "pslist", "dlllist", "handles":
		if pid, ok := options["pid"]; ok {
			args = append(args, "-p", fmt.Sprintf("%v", pid))
		}
	}

	return args, nil
}

// parseVol2Output 解析 Volatility 2 输出
func (e *Executor) parseVol2Output(output []byte, plugin string) (*models.VolatilityResult, error) {
	// vol2 通常输出文本格式，需要解析为结构化数据
	return e.parseTextOutput(output, plugin)
}

// downloadCustomPlugin 下载自定义插件
func (e *Executor) downloadCustomPlugin(ctx context.Context, pluginPath string) (string, error) {
	// 如果是本地路径，直接返回
	if !strings.HasPrefix(pluginPath, "http") && !strings.HasPrefix(pluginPath, "s3://") {
		return pluginPath, nil
	}

	// TODO: 实现从远程位置下载插件的逻辑
	e.logger.WithField("plugin_path", pluginPath).Info("Downloading custom plugin (placeholder)")

	// 暂时返回原路径
	return pluginPath, nil
}

// buildCustomPluginCommand 构建自定义插件命令
func (e *Executor) buildCustomPluginCommand(pluginPath, dumpPath, osType string, options map[string]interface{}) (*exec.Cmd, error) {
	// 假设自定义插件是 Python 脚本
	args := []string{
		pluginPath,
		"-f", dumpPath,
	}

	// 添加其他参数
	if osType != "" {
		args = append(args, "--os", osType)
	}

	cmd := exec.Command("python3", args...)
	return cmd, nil
}

// parseCustomPluginOutput 解析自定义插件输出
func (e *Executor) parseCustomPluginOutput(output []byte, pluginName string) (*models.VolatilityResult, error) {
	// 尝试解析为 JSON，如果失败则解析为文本
	result := &models.VolatilityResult{
		Plugin: pluginName,
	}

	var jsonData []map[string]interface{}
	if err := json.Unmarshal(output, &jsonData); err == nil {
		result.RawData = jsonData
		result.ProcessedData = jsonData
	} else {
		// 解析为文本
		textResult, err := e.parseTextOutput(output, pluginName)
		if err != nil {
			return nil, err
		}
		result.RawData = textResult.RawData
		result.ProcessedData = textResult.ProcessedData
	}

	return result, nil
}

// ExecuteWithFallback 执行插件，自动处理 Vol2 回退
func (e *Executor) ExecuteWithFallback(ctx context.Context, plugin, dumpPath, osType string, options map[string]interface{}) (*models.VolatilityResult, error) {
	return e.vol2Handler.ExecuteWithFallback(ctx, plugin, dumpPath, osType, options)
}

// ExecuteVol2Plugin 直接执行 Vol2 插件
func (e *Executor) ExecuteVol2Plugin(ctx context.Context, plugin, dumpPath, osType string, options map[string]interface{}) (*models.VolatilityResult, error) {
	return e.vol2Handler.executeVol2(ctx, plugin, dumpPath, osType, options)
}

// KillProcess 终止指定进程
func (e *Executor) KillProcess(processID string) error {
	return e.procMgr.KillProcess(processID)
}

// ListActiveProcesses 列出活跃进程
func (e *Executor) ListActiveProcesses() []ProcessInfo {
	return e.procMgr.ListActiveProcesses()
}

// GetProcessStats 获取进程统计信息
func (e *Executor) GetProcessStats() map[string]interface{} {
	return map[string]interface{}{
		"active_processes": e.procMgr.GetProcessCount(),
		"max_concurrent":   e.procMgr.GetMaxConcurrent(),
	}
}

// Cleanup 清理所有活跃进程
func (e *Executor) Cleanup() {
	e.procMgr.Cleanup()
}
