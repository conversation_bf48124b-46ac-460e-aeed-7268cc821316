package volatility

import (
	"bufio"
	"context"
	"fmt"
	"os/exec"
	"regexp"
	"strings"
	"time"

	"memory-go/internal/models"
)

// Vol2Handler Volatility 2 处理器
type Vol2Handler struct {
	executor *Executor
}

// NewVol2Handler 创建 Vol2 处理器
func NewVol2Handler(executor *Executor) *Vol2Handler {
	return &Vol2Handler{
		executor: executor,
	}
}

// ExecuteWithFallback 执行插件，如果 Vol3 失败则回退到 Vol2
func (v *Vol2Handler) ExecuteWithFallback(ctx context.Context, plugin, dumpPath, osType string, options map[string]interface{}) (*models.VolatilityResult, error) {
	// 首先尝试 Vol3
	result, err := v.executor.ExecutePlugin(ctx, plugin, dumpPath, osType, options)
	if err == nil {
		return result, nil
	}

	v.executor.logger.WithError(err).WithField("plugin", plugin).Warn("Vol3 failed, attempting Vol2 fallback")

	// Vol3 失败，尝试 Vol2
	return v.executeVol2(ctx, plugin, dumpPath, osType, options)
}

// executeVol2 执行 Volatility 2 插件
func (v *Vol2Handler) executeVol2(ctx context.Context, plugin, dumpPath, osType string, options map[string]interface{}) (*models.VolatilityResult, error) {
	startTime := time.Now()

	// 检测 profile
	profile, err := v.detectProfile(ctx, dumpPath, osType)
	if err != nil {
		v.executor.logger.WithError(err).Warn("Failed to detect profile, using default")
		profile = v.getDefaultProfile(osType)
	}

	// 转换插件名
	vol2Plugin := v.convertVol3ToVol2Plugin(plugin)

	// 构建命令
	cmd, err := v.buildVol2Command(vol2Plugin, dumpPath, profile, options)
	if err != nil {
		return nil, fmt.Errorf("failed to build vol2 command: %w", err)
	}

	// 执行命令
	output, err := v.executor.runCommand(ctx, cmd)
	if err != nil {
		return nil, fmt.Errorf("vol2 execution failed: %w", err)
	}

	// 解析结果
	result, err := v.parseVol2Output(output, vol2Plugin, plugin)
	if err != nil {
		return nil, fmt.Errorf("failed to parse vol2 output: %w", err)
	}

	result.Duration = time.Since(startTime)
	result.Success = true

	v.executor.logger.WithFields(map[string]interface{}{
		"plugin":      plugin,
		"vol2_plugin": vol2Plugin,
		"profile":     profile,
		"duration":    result.Duration,
		"records":     len(result.ProcessedData),
	}).Info("Vol2 fallback execution completed")

	return result, nil
}

// detectProfile 检测内存转储的 profile
func (v *Vol2Handler) detectProfile(ctx context.Context, dumpPath, osType string) (string, error) {
	// 使用 imageinfo 检测 profile
	cmd := exec.Command(v.executor.config.PythonPath, v.executor.config.Vol2Path, "-f", dumpPath, "imageinfo")

	// 设置超时
	timeout := 60 * time.Second // imageinfo 可能需要较长时间
	cmdCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	cmd = exec.CommandContext(cmdCtx, cmd.Path, cmd.Args[1:]...)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return "", fmt.Errorf("imageinfo execution failed: %w", err)
	}

	return v.parseImageInfo(string(output))
}

// parseImageInfo 解析 imageinfo 输出
func (v *Vol2Handler) parseImageInfo(output string) (string, error) {
	lines := strings.Split(output, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 查找 "Suggested Profile(s)" 行
		if strings.Contains(line, "Suggested Profile(s)") {
			parts := strings.Split(line, ":")
			if len(parts) > 1 {
				profiles := strings.TrimSpace(parts[1])
				if profiles != "" {
					// 取第一个建议的 profile
					firstProfile := strings.Split(profiles, ",")[0]
					profile := strings.TrimSpace(firstProfile)

					// 清理 profile 名称
					profile = v.cleanProfileName(profile)
					return profile, nil
				}
			}
		}
	}

	return "", fmt.Errorf("no profile found in imageinfo output")
}

// cleanProfileName 清理 profile 名称
func (v *Vol2Handler) cleanProfileName(profile string) string {
	// 移除括号中的内容
	re := regexp.MustCompile(`\([^)]*\)`)
	profile = re.ReplaceAllString(profile, "")

	// 移除多余的空格
	profile = strings.TrimSpace(profile)

	return profile
}

// convertVol3ToVol2Plugin 将 Vol3 插件名转换为 Vol2 插件名
func (v *Vol2Handler) convertVol3ToVol2Plugin(vol3Plugin string) string {
	// 详细的插件映射表
	pluginMap := map[string]string{
		// Windows 插件
		"windows.pslist.PsList":           "pslist",
		"windows.dlllist.DllList":         "dlllist",
		"windows.handles.Handles":         "handles",
		"windows.filescan.FileScan":       "filescan",
		"windows.netscan.NetScan":         "netscan",
		"windows.memmap.Memmap":           "memmap",
		"windows.vadinfo.VadInfo":         "vadinfo",
		"windows.modules.Modules":         "modules",
		"windows.symlinkscan.SymlinkScan": "symlinkscan",
		"windows.hivelist.HiveList":       "hivelist",
		"windows.printkey.PrintKey":       "printkey",
		"windows.hashdump.Hashdump":       "hashdump",
		"windows.lsadump.Lsadump":         "lsadump",

		// Linux 插件
		"linux.pslist.PsList":     "linux_pslist",
		"linux.lsmod.Lsmod":       "linux_lsmod",
		"linux.maps.Maps":         "linux_proc_maps",
		"linux.sockstat.Sockstat": "linux_netstat",
		"linux.elfs.Elfs":         "linux_dump_map",
		"linux.envars.Envars":     "linux_psenv",

		// 特殊的 vol2 专用插件
		"vol2.unloadedmodules":     "unloadedmodules",
		"vol2.thrdscan":            "thrdscan",
		"vol2.iehistory":           "iehistory",
		"vol2.cmdscan":             "cmdscan",
		"vol2.enumfunc":            "enumfunc",
		"vol2.sockscan":            "sockscan",
		"vol2.linux_memmap":        "linux_memmap",
		"vol2.linux_moddump":       "linux_moddump",
		"vol2.linux_tmpfs":         "linux_tmpfs",
		"vol2.linux_arp":           "linux_arp",
		"vol2.linux_ifconfig":      "linux_ifconfig",
		"vol2.linux_route_cache":   "linux_route_cache",
		"vol2.linux_pkt_queues":    "linux_pkt_queues",
		"vol2.linux_sk_buff_cache": "linux_sk_buff_cache",
	}

	if vol2Plugin, exists := pluginMap[vol3Plugin]; exists {
		return vol2Plugin
	}

	// 如果没有直接映射，尝试从插件名提取
	if strings.HasPrefix(vol3Plugin, "vol2.") {
		return strings.TrimPrefix(vol3Plugin, "vol2.")
	}

	// 提取最后一部分作为插件名
	parts := strings.Split(vol3Plugin, ".")
	if len(parts) > 0 {
		return strings.ToLower(parts[len(parts)-1])
	}

	return vol3Plugin
}

// getDefaultProfile 获取默认 profile
func (v *Vol2Handler) getDefaultProfile(osType string) string {
	switch strings.ToLower(osType) {
	case "windows":
		return "Win10x64_19041"
	case "linux":
		return "LinuxUbuntu1804x64"
	default:
		return "Win10x64_19041" // 默认使用 Windows
	}
}

// buildVol2Command 构建 Vol2 命令
func (v *Vol2Handler) buildVol2Command(plugin, dumpPath, profile string, options map[string]interface{}) (*exec.Cmd, error) {
	args := []string{
		v.executor.config.Vol2Path,
		"-f", dumpPath,
		plugin,
	}

	// 添加 profile
	if profile != "" {
		args = append(args, "--profile", profile)
	}

	// 添加插件特定参数
	pluginArgs := v.buildVol2PluginArgs(plugin, options)
	args = append(args, pluginArgs...)

	// 添加输出格式（如果插件支持）
	if v.supportsOutputFormat(plugin) {
		args = append(args, "--output", "json")
	}

	cmd := exec.Command(v.executor.config.PythonPath, args...)

	v.executor.logger.WithField("command", strings.Join(cmd.Args, " ")).Debug("Built vol2 command")
	return cmd, nil
}

// buildVol2PluginArgs 构建 Vol2 插件特定参数
func (v *Vol2Handler) buildVol2PluginArgs(plugin string, options map[string]interface{}) []string {
	var args []string

	switch plugin {
	case "pslist", "dlllist", "handles", "vadinfo", "memmap":
		if pid, ok := options["pid"]; ok {
			args = append(args, "-p", fmt.Sprintf("%v", pid))
		}
	case "linux_pslist":
		if pid, ok := options["pid"]; ok {
			args = append(args, "-p", fmt.Sprintf("%v", pid))
		}
	case "printkey":
		if key, ok := options["key"]; ok {
			args = append(args, "-K", fmt.Sprintf("%v", key))
		}
	case "linux_memmap", "linux_moddump":
		if pid, ok := options["pid"]; ok {
			args = append(args, "-p", fmt.Sprintf("%v", pid))
		}
		// 设置输出目录
		if v.executor.config.CachePath != "" {
			args = append(args, "-D", v.executor.config.CachePath)
		}
	}

	return args
}

// supportsOutputFormat 检查插件是否支持输出格式选项
func (v *Vol2Handler) supportsOutputFormat(plugin string) bool {
	// 大多数 vol2 插件不支持 JSON 输出
	supportedPlugins := map[string]bool{
		"pslist":   false,
		"dlllist":  false,
		"handles":  false,
		"filescan": false,
		"netscan":  false,
	}

	supported, exists := supportedPlugins[plugin]
	return exists && supported
}

// parseVol2Output 解析 Vol2 输出
func (v *Vol2Handler) parseVol2Output(output []byte, vol2Plugin, originalPlugin string) (*models.VolatilityResult, error) {
	result := &models.VolatilityResult{
		Plugin:        originalPlugin,
		RawData:       make([]map[string]interface{}, 0),
		ProcessedData: make([]map[string]interface{}, 0),
		FileOutputs:   make([]models.FileOutput, 0),
	}

	// Vol2 通常输出表格格式，需要解析
	lines := strings.Split(string(output), "\n")

	// 查找表格头部
	headerIndex := -1
	var headers []string

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 跳过 Volatility 的版本信息和其他元数据
		if strings.Contains(line, "Volatility") ||
			strings.Contains(line, "Location") ||
			strings.Contains(line, "Offset") && strings.Contains(line, "Name") {
			// 这可能是表格头部
			headers = v.parseTableHeader(line)
			if len(headers) > 0 {
				headerIndex = i
				break
			}
		}
	}

	if headerIndex == -1 || len(headers) == 0 {
		// 没有找到表格头部，按行处理
		return v.parseVol2TextOutput(output, originalPlugin)
	}

	// 解析表格数据
	for i := headerIndex + 1; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])
		if line == "" {
			continue
		}

		// 跳过分隔线
		if strings.Contains(line, "---") || strings.Contains(line, "===") {
			continue
		}

		// 解析数据行
		values := v.parseTableRow(line, len(headers))
		if len(values) > 0 {
			record := make(map[string]interface{})
			for j, header := range headers {
				if j < len(values) {
					record[header] = values[j]
				}
			}

			result.RawData = append(result.RawData, record)
			result.ProcessedData = append(result.ProcessedData, record)
		}
	}

	return result, nil
}

// parseTableHeader 解析表格头部
func (v *Vol2Handler) parseTableHeader(line string) []string {
	// 简单的头部解析，按空格分割
	fields := strings.Fields(line)
	var headers []string

	for _, field := range fields {
		field = strings.TrimSpace(field)
		if field != "" {
			headers = append(headers, field)
		}
	}

	return headers
}

// parseTableRow 解析表格行
func (v *Vol2Handler) parseTableRow(line string, expectedColumns int) []string {
	// 简单的行解析，按空格分割
	fields := strings.Fields(line)

	// 如果字段数量不匹配，可能需要更复杂的解析
	if len(fields) < expectedColumns {
		// 尝试按固定宽度解析（这里简化处理）
		return fields
	}

	return fields
}

// parseVol2TextOutput 解析 Vol2 文本输出（当无法识别表格格式时）
func (v *Vol2Handler) parseVol2TextOutput(output []byte, plugin string) (*models.VolatilityResult, error) {
	result := &models.VolatilityResult{
		Plugin:        plugin,
		RawData:       make([]map[string]interface{}, 0),
		ProcessedData: make([]map[string]interface{}, 0),
		FileOutputs:   make([]models.FileOutput, 0),
	}

	scanner := bufio.NewScanner(strings.NewReader(string(output)))
	lineNumber := 0

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue
		}

		lineNumber++

		// 跳过 Volatility 元数据
		if strings.Contains(line, "Volatility") ||
			strings.Contains(line, "Location") ||
			strings.HasPrefix(line, "#") {
			continue
		}

		record := map[string]interface{}{
			"line_number": lineNumber,
			"content":     line,
		}

		result.RawData = append(result.RawData, record)
		result.ProcessedData = append(result.ProcessedData, record)
	}

	return result, nil
}
