package volatility

import (
	"context"
	"fmt"
	"os/exec"
	"sync"
	"syscall"
	"time"

	"memory-go/pkg/logger"

	"github.com/sirupsen/logrus"
)

// ProcessManager 进程管理器
type ProcessManager struct {
	logger        *logger.Logger
	activeProcs   map[string]*ManagedProcess
	mutex         sync.RWMutex
	maxConcurrent int
	semaphore     chan struct{}
}

// ManagedProcess 管理的进程
type ManagedProcess struct {
	ID        string
	Cmd       *exec.Cmd
	StartTime time.Time
	Context   context.Context
	Cancel    context.CancelFunc
	Done      chan error
	Logger    *logrus.Entry
}

// NewProcessManager 创建进程管理器
func NewProcessManager(maxConcurrent int, logger *logger.Logger) *ProcessManager {
	return &ProcessManager{
		logger:        logger,
		activeProcs:   make(map[string]*ManagedProcess),
		maxConcurrent: maxConcurrent,
		semaphore:     make(chan struct{}, maxConcurrent),
	}
}

// ExecuteCommand 执行命令
func (pm *ProcessManager) ExecuteCommand(ctx context.Context, cmd *exec.Cmd, processID string, timeout time.Duration) ([]byte, error) {
	// 获取并发许可
	select {
	case pm.semaphore <- struct{}{}:
		defer func() { <-pm.semaphore }()
	case <-ctx.Done():
		return nil, ctx.Err()
	}

	// 创建带超时的上下文
	procCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	// 创建管理的进程
	managedProc := &ManagedProcess{
		ID:        processID,
		Cmd:       exec.CommandContext(procCtx, cmd.Path, cmd.Args[1:]...),
		StartTime: time.Now(),
		Context:   procCtx,
		Cancel:    cancel,
		Done:      make(chan error, 1),
		Logger:    pm.logger.WithField("process_id", processID),
	}

	// 复制环境变量
	managedProc.Cmd.Env = cmd.Env

	// 注册进程
	pm.registerProcess(managedProc)
	defer pm.unregisterProcess(processID)

	// 启动进程
	managedProc.Logger.WithFields(map[string]interface{}{
		"command": managedProc.Cmd.String(),
		"timeout": timeout,
	}).Info("Starting process")

	// 用于存储输出的变量
	var processOutput []byte
	var processError error

	// 异步执行
	go func() {
		defer close(managedProc.Done)

		output, err := managedProc.Cmd.CombinedOutput()
		processOutput = output // 保存输出
		processError = err     // 保存错误

		if err != nil {
			managedProc.Logger.WithError(err).WithField("output", string(output)).Error("Process execution failed")
			managedProc.Done <- fmt.Errorf("process execution failed: %w, output: %s", err, string(output))
		} else {
			managedProc.Logger.WithFields(map[string]interface{}{
				"duration":    time.Since(managedProc.StartTime),
				"output_size": len(output),
			}).Info("Process completed successfully")
			managedProc.Done <- nil
		}
	}()

	// 等待完成或取消
	select {
	case err := <-managedProc.Done:
		if err != nil {
			return processOutput, processError
		}
		return processOutput, nil

	case <-procCtx.Done():
		// 超时或取消
		pm.killProcess(managedProc)
		return nil, procCtx.Err()
	}
}

// KillProcess 终止指定进程
func (pm *ProcessManager) KillProcess(processID string) error {
	pm.mutex.RLock()
	proc, exists := pm.activeProcs[processID]
	pm.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("process %s not found", processID)
	}

	return pm.killProcess(proc)
}

// killProcess 终止进程
func (pm *ProcessManager) killProcess(proc *ManagedProcess) error {
	proc.Logger.Info("Terminating process")

	// 取消上下文
	proc.Cancel()

	if proc.Cmd.Process != nil {
		// 首先尝试优雅终止
		if err := proc.Cmd.Process.Signal(syscall.SIGTERM); err != nil {
			proc.Logger.WithError(err).Warn("Failed to send SIGTERM, trying SIGKILL")

			// 如果优雅终止失败，强制终止
			if killErr := proc.Cmd.Process.Kill(); killErr != nil {
				proc.Logger.WithError(killErr).Error("Failed to kill process")
				return killErr
			}
		} else {
			// 等待一段时间让进程优雅退出
			done := make(chan error, 1)
			go func() {
				done <- proc.Cmd.Wait()
			}()

			select {
			case <-done:
				proc.Logger.Info("Process terminated gracefully")
			case <-time.After(5 * time.Second):
				// 超时后强制终止
				proc.Logger.Warn("Process did not terminate gracefully, forcing kill")
				if killErr := proc.Cmd.Process.Kill(); killErr != nil {
					proc.Logger.WithError(killErr).Error("Failed to force kill process")
					return killErr
				}
			}
		}
	}

	return nil
}

// ListActiveProcesses 列出活跃进程
func (pm *ProcessManager) ListActiveProcesses() []ProcessInfo {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	processes := make([]ProcessInfo, 0, len(pm.activeProcs))
	for _, proc := range pm.activeProcs {
		info := ProcessInfo{
			ID:        proc.ID,
			Command:   proc.Cmd.String(),
			StartTime: proc.StartTime,
			Duration:  time.Since(proc.StartTime),
		}

		if proc.Cmd.Process != nil {
			info.PID = proc.Cmd.Process.Pid
		}

		processes = append(processes, info)
	}

	return processes
}

// GetProcessCount 获取活跃进程数量
func (pm *ProcessManager) GetProcessCount() int {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	return len(pm.activeProcs)
}

// GetMaxConcurrent 获取最大并发数
func (pm *ProcessManager) GetMaxConcurrent() int {
	return pm.maxConcurrent
}

// registerProcess 注册进程
func (pm *ProcessManager) registerProcess(proc *ManagedProcess) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	pm.activeProcs[proc.ID] = proc
}

// unregisterProcess 注销进程
func (pm *ProcessManager) unregisterProcess(processID string) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	delete(pm.activeProcs, processID)
}

// ProcessInfo 进程信息
type ProcessInfo struct {
	ID        string        `json:"id"`
	PID       int           `json:"pid"`
	Command   string        `json:"command"`
	StartTime time.Time     `json:"start_time"`
	Duration  time.Duration `json:"duration"`
}

// Cleanup 清理所有活跃进程
func (pm *ProcessManager) Cleanup() {
	pm.mutex.RLock()
	processes := make([]*ManagedProcess, 0, len(pm.activeProcs))
	for _, proc := range pm.activeProcs {
		processes = append(processes, proc)
	}
	pm.mutex.RUnlock()

	pm.logger.WithField("count", len(processes)).Info("Cleaning up active processes")

	// 并发终止所有进程
	var wg sync.WaitGroup
	for _, proc := range processes {
		wg.Add(1)
		go func(p *ManagedProcess) {
			defer wg.Done()
			if err := pm.killProcess(p); err != nil {
				pm.logger.WithError(err).WithField("process_id", p.ID).Error("Failed to cleanup process")
			}
		}(proc)
	}

	// 等待所有进程终止，最多等待 30 秒
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		pm.logger.Info("All processes cleaned up successfully")
	case <-time.After(30 * time.Second):
		pm.logger.Warn("Process cleanup timed out")
	}
}
