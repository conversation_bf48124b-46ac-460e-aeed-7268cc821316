package processor

import (
	"strings"
)

// FieldMappings 字段映射配置，与 Python 版本完全一致
var FieldMappings = map[string]map[string][]string{
	"windows": WindowsPluginFields,
	"linux":   LinuxPluginFields,
}

// WindowsPluginFields Windows 插件字段映射表
// 与 Python 版本的 PLUGIN_KAFKA_KEY_WINDOWS 完全一致
var WindowsPluginFields = map[string][]string{
	"PsList": {"__children", "pid", "ppid", "image_file_name", "offset", "threads", "handles", "session_id", "wow64",
		"create_time", "exit_time", "file_output"},
	"DllList": {"__children", "pid", "process", "base", "size", "name", "path", "load_time", "file_output",
		"load_count"},
	"Handles":        {"__children", "pid", "process", "offset", "handle_value", "type", "granted_access", "name"},
	"GetSIDs":        {"__children", "pid", "process", "sid", "name"},
	"Envars":         {"__children", "pid", "process", "block", "variable", "value"},
	"VerInfo":        {"__children", "pid", "process", "base", "name", "major", "minor", "product", "build"},
	"Memmap":         {"__children", "virtual", "physical", "size", "offset_in_file", "file_output"},
	"VadInfo":        {"__children", "pid", "process", "offset", "start_vpn", "end_vpn", "tag", "protection", "commit_charge", "private_memory", "parent", "file", "file_output"},
	"Modules":        {"__children", "offset", "base", "size", "name", "path", "file_output"},
	"FileScan":       {"__children", "offset", "name", "size", "ptr", "hnd", "access"},
	"SymlinkScan":    {"__children", "offset", "create_time", "from_name", "to_name"},
	"NetScan":        {"__children", "offset", "proto", "local_addr", "local_port", "foreign_addr", "foreign_port", "state", "pid", "owner", "created"},
	"HiveList":       {"__children", "offset", "file_full_path", "file_output"},
	"PrintKey":       {"__children", "last_write_time", "hive_offset", "type", "key", "name", "data", "volatile"},
	"GetServiceSIDs": {"__children", "sid", "service"},
	"Hashdump":       {"__children", "user", "rid", "lmhash", "nthash"},
	"Lsadump":        {"__children", "key", "secret", "hex"},

	// Vol2 专用插件
	"vol2.unloadedmodules": {"name", "start_address", "end_address", "time"},
	"vol2.thrdscan":        {"offset", "pid", "tid", "start_address", "create_time", "exit_time"},
	"vol2.iehistory":       {"process", "pid", "cache_type", "offset", "record_length", "location", "last_modified", "last_accessed", "length", "file_offset", "data_offset", "data_size", "file", "data"},
	"vol2.cmdscan":         {"process", "pid", "history_offset", "application", "flags", "command_count", "last_added", "last_displayed", "first_command", "command_count_max", "handle", "command_number", "command_offset", "command"},
	"vol2.enumfunc":        {"process", "type", "module", "ordinal", "address", "name"},
	"vol2.sockscan":        {"offset", "pid", "port", "proto", "protocol", "address", "create_time"},
}

// LinuxPluginFields Linux 插件字段映射表
// 与 Python 版本的 PLUGIN_KAFKA_KEY_LINUX 完全一致
var LinuxPluginFields = map[string][]string{
	"PsList":   {"__children", "offset", "pid", "tid", "ppid", "comm", "file_output", "uid", "gid", "start_time"},
	"Maps":     {"__children", "pid", "process", "start", "end", "flags", "pg_off", "major", "minor", "inode", "file_path", "file_output"},
	"Lsmod":    {"__children", "offset", "name", "size", "parameter"},
	"Sockstat": {"__children", "net_NS", "pid", "fd", "sock_offset", "family", "type", "proto", "source_addr", "source_port", "destination_addr", "destination_port", "state", "filter"},
	"Elfs":     {"__children", "pid", "process", "start", "end", "file_path", "file_output"},
	"Envars":   {"__children", "pid", "ppid", "comm", "key", "value"},
	"Envvars":  {"__children", "pid", "ppid", "comm", "key", "value"},
	"PsScan":   {"__children", "offset", "pid", "tid", "ppid", "comm", "exit_state"},

	// Vol2 专用 Linux 插件
	"vol2.linux_memmap":        {"Task", "pid", "virtual", "physical", "size", "filename"},
	"vol2.linux_moddump":       {"name", "offset", "filename"},
	"vol2.linux_tmpfs":         {"file_name", "path"},
	"vol2.linux_arp":           {"data"},
	"vol2.linux_ifconfig":      {"interface", "ip", "mac", "promiscuous"},
	"vol2.linux_route_cache":   {"interface", "destination", "gateway"},
	"vol2.linux_pkt_queues":    {"file_name"},
	"vol2.linux_sk_buff_cache": {"file_name"},
}

// GetFieldMapping 获取指定操作系统和插件的字段映射
func GetFieldMapping(osType, plugin string) ([]string, bool) {
	osFields, exists := FieldMappings[osType]
	if !exists {
		return nil, false
	}

	fields, exists := osFields[plugin]
	return fields, exists
}

// GetPluginName 从完整插件名中提取插件名
func GetPluginName(fullPluginName string) string {
	// 处理 vol2 插件
	if fullPluginName == "vol2.enumfunc" {
		return "vol2.enumfunc"
	}

	// 处理其他 vol2 插件
	if len(fullPluginName) > 5 && fullPluginName[:5] == "vol2." {
		return fullPluginName
	}

	// 处理 vol3 插件，提取最后一部分
	parts := strings.Split(fullPluginName, ".")
	if len(parts) > 0 {
		return parts[len(parts)-1]
	}

	return fullPluginName
}

// IsVol2Plugin 检查是否为 vol2 插件
func IsVol2Plugin(pluginName string) bool {
	return len(pluginName) > 5 && pluginName[:5] == "vol2."
}

// GetSupportedPlugins 获取支持的插件列表
func GetSupportedPlugins(osType string) []string {
	osFields, exists := FieldMappings[osType]
	if !exists {
		return nil
	}

	plugins := make([]string, 0, len(osFields))
	for plugin := range osFields {
		plugins = append(plugins, plugin)
	}

	return plugins
}
