package processor

import (
	"encoding/hex"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"memory-go/pkg/logger"
)

// TypeConverter 类型转换器，等效于 Python 版本的 DictRenderer
type TypeConverter struct {
	logger *logger.Logger
}

// NewTypeConverter 创建类型转换器
func NewTypeConverter(log *logger.Logger) *TypeConverter {
	return &TypeConverter{
		logger: log,
	}
}

// ConvertValue 转换值，处理各种特殊数据类型
func (tc *TypeConverter) ConvertValue(value interface{}, fieldName string) interface{} {
	if value == nil {
		return nil
	}

	// 处理 BaseAbsentValue（缺失值）
	if tc.isAbsentValue(value) {
		return nil
	}

	valueStr := fmt.Sprintf("%v", value)

	// 处理不同类型的数据
	switch {
	case tc.isHexBytes(valueStr):
		return tc.convertHexBytes(valueStr)
	case tc.isDisassembly(valueStr):
		return tc.convertDisassembly(valueStr)
	case tc.isMultiTypeData(valueStr):
		return tc.convertMultiTypeData(valueStr)
	case tc.isBytesData(valueStr):
		return tc.convertBytes(valueStr)
	case tc.isDateTime(valueStr):
		return tc.convertDateTime(valueStr)
	case tc.isNumericField(fieldName):
		return tc.convertNumeric(valueStr)
	case tc.isBooleanField(fieldName):
		return tc.convertBoolean(valueStr)
	default:
		return tc.convertDefault(valueStr)
	}
}

// isAbsentValue 检查是否为缺失值
func (tc *TypeConverter) isAbsentValue(value interface{}) bool {
	valueStr := fmt.Sprintf("%v", value)
	absentValues := []string{
		"BaseAbsentValue", "N/A", "NULL", "null", "None", "none",
		"<absent>", "<missing>", "<unavailable>", "-",
	}

	for _, absent := range absentValues {
		if valueStr == absent {
			return true
		}
	}

	return false
}

// isHexBytes 检查是否为十六进制字节数据
func (tc *TypeConverter) isHexBytes(value string) bool {
	// 匹配类似 "0x41424344" 或 "41 42 43 44" 的格式
	hexPattern := regexp.MustCompile(`^(0x)?[0-9a-fA-F\s]+$`)
	return len(value) > 2 && hexPattern.MatchString(value)
}

// convertHexBytes 转换十六进制字节数据
func (tc *TypeConverter) convertHexBytes(value string) string {
	// 清理格式
	cleaned := strings.ReplaceAll(value, "0x", "")
	cleaned = strings.ReplaceAll(cleaned, " ", "")

	// 验证是否为有效的十六进制
	if _, err := hex.DecodeString(cleaned); err != nil {
		return value // 如果无法解码，返回原值
	}

	// 格式化为标准的十六进制表示
	if len(cleaned)%2 != 0 {
		cleaned = "0" + cleaned
	}

	// 每两个字符添加一个空格
	var formatted strings.Builder
	for i := 0; i < len(cleaned); i += 2 {
		if i > 0 {
			formatted.WriteString(" ")
		}
		formatted.WriteString(cleaned[i : i+2])
	}

	return formatted.String()
}

// isDisassembly 检查是否为反汇编代码
func (tc *TypeConverter) isDisassembly(value string) bool {
	// 匹配汇编指令格式
	asmPattern := regexp.MustCompile(`^\s*[0-9a-fA-F]+:\s+[0-9a-fA-F\s]+\s+\w+`)
	return asmPattern.MatchString(value)
}

// convertDisassembly 转换反汇编代码
func (tc *TypeConverter) convertDisassembly(value string) map[string]interface{} {
	lines := strings.Split(value, "\n")
	instructions := make([]map[string]interface{}, 0)

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 解析汇编指令行
		if instruction := tc.parseAsmInstruction(line); instruction != nil {
			instructions = append(instructions, instruction)
		}
	}

	return map[string]interface{}{
		"type":         "disassembly",
		"instructions": instructions,
	}
}

// parseAsmInstruction 解析汇编指令
func (tc *TypeConverter) parseAsmInstruction(line string) map[string]interface{} {
	// 匹配格式: "地址: 字节码 指令"
	pattern := regexp.MustCompile(`^([0-9a-fA-F]+):\s+([0-9a-fA-F\s]+)\s+(.+)$`)
	matches := pattern.FindStringSubmatch(line)

	if len(matches) != 4 {
		return map[string]interface{}{
			"raw": line,
		}
	}

	return map[string]interface{}{
		"address":     matches[1],
		"bytes":       strings.TrimSpace(matches[2]),
		"instruction": strings.TrimSpace(matches[3]),
	}
}

// isMultiTypeData 检查是否为多类型数据
func (tc *TypeConverter) isMultiTypeData(value string) bool {
	// 检查是否包含多种数据类型的标识
	indicators := []string{
		"<struct>", "<union>", "<array>", "<pointer>",
		"type:", "size:", "offset:",
	}

	for _, indicator := range indicators {
		if strings.Contains(value, indicator) {
			return true
		}
	}

	return false
}

// convertMultiTypeData 转换多类型数据
func (tc *TypeConverter) convertMultiTypeData(value string) map[string]interface{} {
	// 简单的多类型数据解析
	result := map[string]interface{}{
		"type": "multitype",
		"raw":  value,
	}

	// 尝试提取结构化信息
	if strings.Contains(value, "size:") {
		if size := tc.extractValue(value, "size:"); size != "" {
			if sizeInt, err := strconv.ParseInt(size, 10, 64); err == nil {
				result["size"] = sizeInt
			}
		}
	}

	if strings.Contains(value, "offset:") {
		if offset := tc.extractValue(value, "offset:"); offset != "" {
			if offsetInt, err := strconv.ParseInt(offset, 10, 64); err == nil {
				result["offset"] = offsetInt
			}
		}
	}

	return result
}

// extractValue 从文本中提取值
func (tc *TypeConverter) extractValue(text, key string) string {
	pattern := regexp.MustCompile(key + `\s*([^\s,]+)`)
	matches := pattern.FindStringSubmatch(text)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

// isBytesData 检查是否为字节数据
func (tc *TypeConverter) isBytesData(value string) bool {
	// 检查是否为字节数组格式
	return strings.HasPrefix(value, "b'") && strings.HasSuffix(value, "'") ||
		strings.HasPrefix(value, "bytes(") && strings.HasSuffix(value, ")")
}

// convertBytes 转换字节数据
func (tc *TypeConverter) convertBytes(value string) string {
	// 移除字节标识符
	cleaned := value
	if strings.HasPrefix(cleaned, "b'") && strings.HasSuffix(cleaned, "'") {
		cleaned = cleaned[2 : len(cleaned)-1]
	} else if strings.HasPrefix(cleaned, "bytes(") && strings.HasSuffix(cleaned, ")") {
		cleaned = cleaned[6 : len(cleaned)-1]
	}

	// 转换为十六进制表示
	bytes := []byte(cleaned)
	hexStr := make([]string, len(bytes))
	for i, b := range bytes {
		hexStr[i] = fmt.Sprintf("%02x", b)
	}

	return strings.Join(hexStr, " ")
}

// isDateTime 检查是否为日期时间
func (tc *TypeConverter) isDateTime(value string) bool {
	// 常见的日期时间格式
	dateTimePatterns := []*regexp.Regexp{
		regexp.MustCompile(`^\d{4}-\d{2}-\d{2}`),                                   // YYYY-MM-DD
		regexp.MustCompile(`^\d{2}/\d{2}/\d{4}`),                                   // MM/DD/YYYY
		regexp.MustCompile(`^\w{3}\s+\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2}\s+\d{4}`), // Mon Jan 2 15:04:05 2006
		regexp.MustCompile(`^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}`),                 // ISO format
	}

	for _, pattern := range dateTimePatterns {
		if pattern.MatchString(value) {
			return true
		}
	}

	return false
}

// convertDateTime 转换日期时间
func (tc *TypeConverter) convertDateTime(value string) string {
	// 尝试解析各种时间格式
	timeFormats := []string{
		"2006-01-02 15:04:05",
		"2006-01-02T15:04:05",
		"2006-01-02 15:04:05.000000",
		"2006-01-02T15:04:05.000000Z",
		"01/02/2006 15:04:05",
		"Mon Jan 2 15:04:05 2006",
		"Mon Jan 2 15:04:05 MST 2006",
		"2006-01-02T15:04:05Z07:00",
	}

	for _, format := range timeFormats {
		if t, err := time.Parse(format, value); err == nil {
			// 返回 ISO 格式
			return t.Format("2006-01-02T15:04:05.000Z")
		}
	}

	// 如果无法解析，返回原值
	return value
}

// isNumericField 检查是否为数字字段
func (tc *TypeConverter) isNumericField(fieldName string) bool {
	numericFields := map[string]bool{
		"pid": true, "ppid": true, "tid": true, "offset": true,
		"base": true, "size": true, "threads": true, "handles": true,
		"session_id": true, "handle_value": true, "granted_access": true,
		"rid": true, "major": true, "minor": true, "build": true,
		"start_vpn": true, "end_vpn": true, "commit_charge": true,
		"local_port": true, "foreign_port": true, "port": true,
		"fd": true, "sock_offset": true, "source_port": true,
		"destination_port": true, "uid": true, "gid": true,
		"start_address": true, "end_address": true, "address": true,
		"ordinal": true, "record_length": true, "length": true,
		"file_offset": true, "data_offset": true, "data_size": true,
		"command_count": true, "command_count_max": true,
		"command_number": true, "command_offset": true,
		"virtual": true, "physical": true, "start": true, "end": true,
		"pg_off": true, "inode": true, "load_count": true,
	}

	return numericFields[fieldName]
}

// convertNumeric 转换数字
func (tc *TypeConverter) convertNumeric(value string) interface{} {
	// 清理数字字符串
	cleaned := strings.TrimSpace(value)

	// 处理十六进制
	if strings.HasPrefix(cleaned, "0x") {
		if intVal, err := strconv.ParseInt(cleaned[2:], 16, 64); err == nil {
			return intVal
		}
	}

	// 处理十进制
	if intVal, err := strconv.ParseInt(cleaned, 10, 64); err == nil {
		return intVal
	}

	// 处理浮点数
	if floatVal, err := strconv.ParseFloat(cleaned, 64); err == nil {
		return floatVal
	}

	// 如果无法转换，返回原值
	return value
}

// isBooleanField 检查是否为布尔字段
func (tc *TypeConverter) isBooleanField(fieldName string) bool {
	booleanFields := map[string]bool{
		"wow64": true, "private_memory": true, "volatile": true,
		"promiscuous": true,
	}

	return booleanFields[fieldName]
}

// convertBoolean 转换布尔值
func (tc *TypeConverter) convertBoolean(value string) bool {
	cleaned := strings.ToLower(strings.TrimSpace(value))

	switch cleaned {
	case "true", "yes", "1", "enabled", "on":
		return true
	case "false", "no", "0", "disabled", "off":
		return false
	default:
		// 默认返回 false
		return false
	}
}

// convertDefault 默认转换
func (tc *TypeConverter) convertDefault(value string) interface{} {
	// 清理字符串
	cleaned := strings.TrimSpace(value)

	// 处理空值
	if cleaned == "" || cleaned == "-" || cleaned == "N/A" {
		return nil
	}

	return cleaned
}
