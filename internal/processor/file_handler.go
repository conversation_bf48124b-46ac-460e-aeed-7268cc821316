package processor

import (
	"crypto/md5"
	"crypto/sha1"
	"crypto/sha256"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"memory-go/internal/models"
	"memory-go/pkg/logger"
)

// FileHandler 文件处理器
type FileHandler struct {
	logger *logger.Logger
}

// NewFileHandler 创建文件处理器
func NewFileHandler(log *logger.Logger) *FileHandler {
	return &FileHandler{
		logger: log,
	}
}

// ProcessFileOutputs 处理文件输出
func (fh *FileHandler) ProcessFileOutputs(data []map[string]interface{}, originalFileOutputs []models.FileOutput, pluginName string) []models.FileOutput {
	fileOutputs := make([]models.FileOutput, 0)

	// 处理原始文件输出
	for _, originalOutput := range originalFileOutputs {
		processedOutput := fh.processFileOutput(originalOutput, pluginName)
		fileOutputs = append(fileOutputs, processedOutput)
	}

	// 从处理后的数据中提取文件输出
	for i, record := range data {
		if fileOutput := fh.extractFileOutput(record, pluginName, i); fileOutput != nil {
			fileOutputs = append(fileOutputs, *fileOutput)
		}
	}

	fh.logger.WithFields(map[string]interface{}{
		"plugin":       pluginName,
		"file_outputs": len(fileOutputs),
	}).Info("File outputs processed")

	return fileOutputs
}

// processFileOutput 处理单个文件输出
func (fh *FileHandler) processFileOutput(fileOutput models.FileOutput, pluginName string) models.FileOutput {
	if fileOutput.LocalPath == "" {
		return fileOutput
	}

	// 检查文件是否存在
	if _, err := os.Stat(fileOutput.LocalPath); os.IsNotExist(err) {
		fh.logger.WithFields(map[string]interface{}{
			"plugin":     pluginName,
			"local_path": fileOutput.LocalPath,
		}).Warn("File output does not exist")
		return fileOutput
	}

	// 计算文件大小
	if fileInfo, err := os.Stat(fileOutput.LocalPath); err == nil {
		fileOutput.Size = fileInfo.Size()
	}

	// 计算文件哈希
	if hashes, err := fh.calculateHashes(fileOutput.LocalPath); err == nil {
		fileOutput.Hashes = hashes
	} else {
		fh.logger.WithError(err).WithField("local_path", fileOutput.LocalPath).Warn("Failed to calculate file hashes")
	}

	// 生成远程路径
	if fileOutput.RemotePath == "" {
		fileOutput.RemotePath = fh.generateRemotePath(fileOutput.LocalPath, pluginName)
	}

	return fileOutput
}

// extractFileOutput 从记录中提取文件输出
func (fh *FileHandler) extractFileOutput(record map[string]interface{}, pluginName string, recordIndex int) *models.FileOutput {
	fileOutputValue, exists := record["file_output"]
	if !exists || fileOutputValue == nil {
		return nil
	}

	fileOutputStr := fmt.Sprintf("%v", fileOutputValue)
	if fileOutputStr == "" || fileOutputStr == "N/A" || fileOutputStr == "-" {
		return nil
	}

	fileOutput := &models.FileOutput{
		LocalPath: fileOutputStr,
	}

	// 处理文件输出
	processedOutput := fh.processFileOutput(*fileOutput, pluginName)
	return &processedOutput
}

// calculateHashes 计算文件哈希值
func (fh *FileHandler) calculateHashes(filePath string) (map[string]string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	// 创建哈希计算器
	md5Hash := md5.New()
	sha1Hash := sha1.New()
	sha256Hash := sha256.New()

	// 使用 MultiWriter 同时计算多个哈希
	multiWriter := io.MultiWriter(md5Hash, sha1Hash, sha256Hash)

	// 读取文件并计算哈希
	if _, err := io.Copy(multiWriter, file); err != nil {
		return nil, fmt.Errorf("failed to read file for hashing: %w", err)
	}

	hashes := map[string]string{
		"md5":    fmt.Sprintf("%x", md5Hash.Sum(nil)),
		"sha1":   fmt.Sprintf("%x", sha1Hash.Sum(nil)),
		"sha256": fmt.Sprintf("%x", sha256Hash.Sum(nil)),
	}

	return hashes, nil
}

// generateRemotePath 生成远程路径
func (fh *FileHandler) generateRemotePath(localPath, pluginName string) string {
	// 提取文件名
	fileName := filepath.Base(localPath)

	// 生成远程路径格式：plugin/dump_file/filename
	remotePath := filepath.Join(pluginName, "dump_file", fileName)

	// 确保使用正斜杠（适用于对象存储）
	remotePath = strings.ReplaceAll(remotePath, "\\", "/")

	return remotePath
}

// CleanupTempFiles 清理临时文件
func (fh *FileHandler) CleanupTempFiles(fileOutputs []models.FileOutput) {
	for _, fileOutput := range fileOutputs {
		if fileOutput.LocalPath != "" && fh.isTempFile(fileOutput.LocalPath) {
			if err := os.Remove(fileOutput.LocalPath); err != nil {
				fh.logger.WithError(err).WithField("local_path", fileOutput.LocalPath).Warn("Failed to cleanup temp file")
			} else {
				fh.logger.WithField("local_path", fileOutput.LocalPath).Debug("Temp file cleaned up")
			}
		}
	}
}

// isTempFile 检查是否为临时文件
func (fh *FileHandler) isTempFile(filePath string) bool {
	// 检查是否在临时目录中
	tempDirs := []string{"/tmp", "/temp", os.TempDir()}

	for _, tempDir := range tempDirs {
		if strings.HasPrefix(filePath, tempDir) {
			return true
		}
	}

	// 检查文件名模式
	fileName := filepath.Base(filePath)
	tempPatterns := []string{
		"tmp_", "temp_", "volatility_", "vol_", "dump_",
	}

	for _, pattern := range tempPatterns {
		if strings.HasPrefix(fileName, pattern) {
			return true
		}
	}

	return false
}

// ValidateFileOutput 验证文件输出
func (fh *FileHandler) ValidateFileOutput(fileOutput models.FileOutput) error {
	if fileOutput.LocalPath == "" {
		return fmt.Errorf("local path is empty")
	}

	// 检查文件是否存在
	if _, err := os.Stat(fileOutput.LocalPath); os.IsNotExist(err) {
		return fmt.Errorf("file does not exist: %s", fileOutput.LocalPath)
	}

	// 检查文件大小
	if fileInfo, err := os.Stat(fileOutput.LocalPath); err == nil {
		if fileInfo.Size() == 0 {
			return fmt.Errorf("file is empty: %s", fileOutput.LocalPath)
		}
		if fileInfo.Size() > 100*1024*1024 { // 100MB 限制
			fh.logger.WithFields(map[string]interface{}{
				"local_path": fileOutput.LocalPath,
				"size":       fileInfo.Size(),
			}).Warn("File is very large")
		}
	}

	return nil
}

// GetFileInfo 获取文件信息
func (fh *FileHandler) GetFileInfo(filePath string) (map[string]interface{}, error) {
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}

	info := map[string]interface{}{
		"name":     fileInfo.Name(),
		"size":     fileInfo.Size(),
		"mode":     fileInfo.Mode().String(),
		"mod_time": fileInfo.ModTime().Format("2006-01-02T15:04:05.000Z"),
		"is_dir":   fileInfo.IsDir(),
	}

	// 计算哈希（如果文件不太大）
	if fileInfo.Size() < 10*1024*1024 { // 10MB 以下才计算哈希
		if hashes, err := fh.calculateHashes(filePath); err == nil {
			info["hashes"] = hashes
		}
	}

	return info, nil
}

// CreateOutputDirectory 创建输出目录
func (fh *FileHandler) CreateOutputDirectory(basePath, pluginName string) (string, error) {
	outputDir := filepath.Join(basePath, pluginName, "dump_file")

	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create output directory: %w", err)
	}

	fh.logger.WithField("output_dir", outputDir).Debug("Output directory created")
	return outputDir, nil
}

// GetFileExtension 根据插件类型获取文件扩展名
func (fh *FileHandler) GetFileExtension(pluginName string) string {
	extensionMap := map[string]string{
		"DllList":  ".dll",
		"Modules":  ".sys",
		"FileScan": ".bin",
		"Memmap":   ".dmp",
		"VadInfo":  ".bin",
		"HiveList": ".hive",
		"Elfs":     ".elf",
		"Maps":     ".bin",
	}

	if ext, exists := extensionMap[pluginName]; exists {
		return ext
	}

	return ".bin" // 默认扩展名
}
