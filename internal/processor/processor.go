package processor

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"memory-go/internal/models"
	"memory-go/pkg/logger"
)

// ResultProcessor 结果处理器
type ResultProcessor struct {
	logger        *logger.Logger
	typeConverter *TypeConverter
	fileHandler   *FileHandler
}

// New 创建新的结果处理器
func New(log *logger.Logger) *ResultProcessor {
	return &ResultProcessor{
		logger:        log,
		typeConverter: NewTypeConverter(log),
		fileHandler:   NewFileHandler(log),
	}
}

// Process 处理 Volatility 结果
func (p *ResultProcessor) Process(result *models.VolatilityResult, osType string) (*models.ProcessedResult, error) {
	pluginName := GetPluginName(result.Plugin)

	p.logger.WithFields(map[string]interface{}{
		"plugin":      result.Plugin,
		"plugin_name": pluginName,
		"os_type":     osType,
		"raw_records": len(result.RawData),
	}).Info("Processing Volatility result")

	// 1. 应用字段映射
	mappedData, err := p.applyFieldMapping(result.RawData, osType, pluginName)
	if err != nil {
		return nil, fmt.Errorf("failed to apply field mapping: %w", err)
	}

	// 2. 类型转换和数据清理
	processedData := p.convertTypes(mappedData, pluginName)

	// 3. 处理文件输出
	fileOutputs := p.fileHandler.ProcessFileOutputs(processedData, result.FileOutputs, pluginName)

	processedResult := &models.ProcessedResult{
		Plugin:      result.Plugin,
		Data:        processedData,
		FileOutputs: fileOutputs,
	}

	p.logger.WithFields(map[string]interface{}{
		"plugin":            result.Plugin,
		"processed_records": len(processedData),
		"file_outputs":      len(fileOutputs),
	}).Info("Result processing completed")

	return processedResult, nil
}

// applyFieldMapping 应用字段映射
func (p *ResultProcessor) applyFieldMapping(rawData []map[string]interface{}, osType, pluginName string) ([]map[string]interface{}, error) {
	// 获取字段映射
	fieldMapping, exists := GetFieldMapping(osType, pluginName)
	if !exists {
		p.logger.WithFields(map[string]interface{}{
			"os_type":     osType,
			"plugin_name": pluginName,
		}).Warn("No field mapping found, using raw data")
		return rawData, nil
	}

	mappedData := make([]map[string]interface{}, 0, len(rawData))

	for _, record := range rawData {
		mappedRecord := p.mapRecord(record, fieldMapping)
		mappedData = append(mappedData, mappedRecord)
	}

	return mappedData, nil
}

// mapRecord 映射单个记录
func (p *ResultProcessor) mapRecord(record map[string]interface{}, fieldMapping []string) map[string]interface{} {
	mappedRecord := make(map[string]interface{})

	// 如果原始数据是有序的（来自表格解析），按顺序映射
	if p.isOrderedRecord(record) {
		values := p.extractOrderedValues(record)
		for i, fieldName := range fieldMapping {
			if i < len(values) {
				mappedRecord[fieldName] = values[i]
			} else {
				mappedRecord[fieldName] = nil
			}
		}
	} else {
		// 如果原始数据是键值对，尝试直接映射
		for _, fieldName := range fieldMapping {
			if value, exists := record[fieldName]; exists {
				mappedRecord[fieldName] = value
			} else {
				// 尝试其他可能的字段名
				if altValue := p.findAlternativeField(record, fieldName); altValue != nil {
					mappedRecord[fieldName] = altValue
				} else {
					mappedRecord[fieldName] = nil
				}
			}
		}
	}

	return mappedRecord
}

// isOrderedRecord 检查记录是否为有序记录（来自表格解析）
func (p *ResultProcessor) isOrderedRecord(record map[string]interface{}) bool {
	// 如果包含 line_number 或者字段名都是数字，则认为是有序记录
	if _, exists := record["line_number"]; exists {
		return false // 这是文本解析的结果
	}

	// 检查是否所有键都是数字（表示列索引）
	for key := range record {
		if _, err := strconv.Atoi(key); err != nil {
			return false
		}
	}

	return len(record) > 0
}

// extractOrderedValues 从有序记录中提取值
func (p *ResultProcessor) extractOrderedValues(record map[string]interface{}) []interface{} {
	maxIndex := -1
	for key := range record {
		if index, err := strconv.Atoi(key); err == nil && index > maxIndex {
			maxIndex = index
		}
	}

	if maxIndex < 0 {
		// 如果没有数字键，按原始顺序返回值
		values := make([]interface{}, 0, len(record))
		for _, value := range record {
			values = append(values, value)
		}
		return values
	}

	values := make([]interface{}, maxIndex+1)
	for key, value := range record {
		if index, err := strconv.Atoi(key); err == nil {
			values[index] = value
		}
	}

	return values
}

// findAlternativeField 查找替代字段名
func (p *ResultProcessor) findAlternativeField(record map[string]interface{}, targetField string) interface{} {
	// 字段名映射表
	alternatives := map[string][]string{
		"pid":             {"PID", "Pid"},
		"ppid":            {"PPID", "PPid", "ParentPid"},
		"image_file_name": {"ImageFileName", "Name", "ProcessName"},
		"offset":          {"Offset", "VirtualAddress"},
		"create_time":     {"CreateTime", "CreationTime", "StartTime"},
		"exit_time":       {"ExitTime", "EndTime"},
		"file_output":     {"FileOutput", "DumpFile", "OutputFile"},
		"base":            {"Base", "BaseAddress"},
		"size":            {"Size", "Length"},
		"name":            {"Name", "ModuleName", "FileName"},
		"path":            {"Path", "FullPath", "FilePath"},
	}

	if altNames, exists := alternatives[targetField]; exists {
		for _, altName := range altNames {
			if value, exists := record[altName]; exists {
				return value
			}
		}
	}

	// 尝试不区分大小写的匹配
	lowerTarget := strings.ToLower(targetField)
	for key, value := range record {
		if strings.ToLower(key) == lowerTarget {
			return value
		}
	}

	return nil
}

// convertTypes 类型转换和数据清理
func (p *ResultProcessor) convertTypes(data []map[string]interface{}, pluginName string) []map[string]interface{} {
	convertedData := make([]map[string]interface{}, 0, len(data))

	for _, record := range data {
		convertedRecord := make(map[string]interface{})

		for key, value := range record {
			convertedRecord[key] = p.convertValue(key, value, pluginName)
		}

		// 应用插件特定的时间戳处理
		p.updateTimeFields(convertedRecord, pluginName)

		convertedData = append(convertedData, convertedRecord)
	}

	return convertedData
}

// convertValue 转换单个值
func (p *ResultProcessor) convertValue(fieldName string, value interface{}, pluginName string) interface{} {
	// 使用类型转换器进行转换
	return p.typeConverter.ConvertValue(value, fieldName)
}

// isNumericField 检查是否为数字字段
func (p *ResultProcessor) isNumericField(fieldName string) bool {
	numericFields := map[string]bool{
		"pid": true, "ppid": true, "tid": true, "offset": true,
		"base": true, "size": true, "threads": true, "handles": true,
		"session_id": true, "handle_value": true, "granted_access": true,
		"rid": true, "major": true, "minor": true, "build": true,
		"start_vpn": true, "end_vpn": true, "commit_charge": true,
		"local_port": true, "foreign_port": true, "port": true,
		"fd": true, "sock_offset": true, "source_port": true,
		"destination_port": true, "uid": true, "gid": true,
		"start_address": true, "end_address": true, "address": true,
		"ordinal": true, "record_length": true, "length": true,
		"file_offset": true, "data_offset": true, "data_size": true,
		"command_count": true, "command_count_max": true,
		"command_number": true, "command_offset": true,
		"virtual": true, "physical": true, "start": true, "end": true,
		"pg_off": true, "inode": true,
	}

	return numericFields[fieldName]
}

// isBooleanField 检查是否为布尔字段
func (p *ResultProcessor) isBooleanField(fieldName string) bool {
	booleanFields := map[string]bool{
		"wow64": true, "private_memory": true, "volatile": true,
		"promiscuous": true,
	}

	return booleanFields[fieldName]
}

// isTimeField 检查是否为时间字段
func (p *ResultProcessor) isTimeField(fieldName string) bool {
	timeFields := map[string]bool{
		"create_time": true, "exit_time": true, "load_time": true,
		"last_write_time": true, "created": true, "start_time": true,
		"last_modified": true, "last_accessed": true, "last_added": true,
		"last_displayed": true, "time": true,
	}

	return timeFields[fieldName]
}

// parseTimeValue 解析时间值
func (p *ResultProcessor) parseTimeValue(timeStr string) interface{} {
	// 常见的时间格式
	timeFormats := []string{
		"2006-01-02 15:04:05",
		"2006-01-02T15:04:05",
		"2006-01-02 15:04:05.000000",
		"2006-01-02T15:04:05.000000Z",
		"Mon Jan 2 15:04:05 2006",
	}

	for _, format := range timeFormats {
		if t, err := time.Parse(format, timeStr); err == nil {
			return t.Format("2006-01-02T15:04:05.000Z")
		}
	}

	// 如果无法解析，返回原始字符串
	return timeStr
}

// updateTimeFields 更新时间字段（等效于 Python 版本的 update_time_plugin）
func (p *ResultProcessor) updateTimeFields(record map[string]interface{}, pluginName string) {
	// 根据插件类型处理特定的时间字段
	switch pluginName {
	case "PsList", "pslist":
		p.processCreateTime(record)
	case "DllList", "dlllist":
		p.processLoadTime(record)
	case "Handles", "handles":
		// handles 通常没有时间字段
	case "FileScan", "filescan":
		// filescan 通常没有时间字段
	case "NetScan", "netscan":
		p.processCreatedTime(record)
	}
}

// processCreateTime 处理创建时间
func (p *ResultProcessor) processCreateTime(record map[string]interface{}) {
	if createTime, exists := record["create_time"]; exists && createTime != nil {
		if timeStr := fmt.Sprintf("%v", createTime); timeStr != "" && timeStr != "N/A" {
			if parsedTime := p.parseTimeValue(timeStr); parsedTime != nil {
				record["create_time"] = parsedTime
			}
		} else {
			record["create_time"] = nil
		}
	}
}

// processLoadTime 处理加载时间
func (p *ResultProcessor) processLoadTime(record map[string]interface{}) {
	if loadTime, exists := record["load_time"]; exists && loadTime != nil {
		if timeStr := fmt.Sprintf("%v", loadTime); timeStr != "" && timeStr != "N/A" {
			if parsedTime := p.parseTimeValue(timeStr); parsedTime != nil {
				record["load_time"] = parsedTime
			}
		} else {
			record["load_time"] = nil
		}
	}
}

// processCreatedTime 处理创建时间（网络连接）
func (p *ResultProcessor) processCreatedTime(record map[string]interface{}) {
	if created, exists := record["created"]; exists && created != nil {
		if timeStr := fmt.Sprintf("%v", created); timeStr != "" && timeStr != "N/A" {
			if parsedTime := p.parseTimeValue(timeStr); parsedTime != nil {
				record["created"] = parsedTime
			}
		} else {
			record["created"] = nil
		}
	}
}

// CleanupFiles 清理临时文件
func (p *ResultProcessor) CleanupFiles(fileOutputs []models.FileOutput) {
	p.fileHandler.CleanupTempFiles(fileOutputs)
}
