package messaging

import (
	"testing"
	"time"

	"memory-go/internal/models"
)

func TestMessageFormatter_FormatProgressMessage(t *testing.T) {
	formatter := NewMessageFormatter()

	msg, err := formatter.FormatProgressMessage("flow-123", "pslist", 50.0, "running", "Processing pslist plugin")
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if msg.FlowID != "flow-123" {
		t.<PERSON>("Expected FlowID 'flow-123', got '%s'", msg.FlowID)
	}
	if msg.Plugin != "pslist" {
		t.<PERSON><PERSON><PERSON>("Expected Plugin 'pslist', got '%s'", msg.Plugin)
	}
	if msg.Progress != 50.0 {
		t.<PERSON><PERSON><PERSON>("Expected Progress 50.0, got %f", msg.Progress)
	}
	if msg.Status != "running" {
		t.<PERSON><PERSON>("Expected Status 'running', got '%s'", msg.Status)
	}
}

func TestMessageFormatter_FormatDataMessage(t *testing.T) {
	formatter := NewMessageFormatter()

	msg, err := formatter.FormatDataMessage("flow-123", "pslist", "/results/pslist.json", "completed")
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if msg.FlowID != "flow-123" {
		t.Errorf("Expected FlowID 'flow-123', got '%s'", msg.FlowID)
	}
	if msg.Plugin != "pslist" {
		t.Errorf("Expected Plugin 'pslist', got '%s'", msg.Plugin)
	}
	if msg.ResultPath != "/results/pslist.json" {
		t.Errorf("Expected ResultPath '/results/pslist.json', got '%s'", msg.ResultPath)
	}
	if msg.Status != "completed" {
		t.Errorf("Expected Status 'completed', got '%s'", msg.Status)
	}
}

func TestMessageFormatter_FormatKafkaProgressMessage(t *testing.T) {
	formatter := NewMessageFormatter()

	parseDict := map[string]interface{}{
		"extra": map[string]interface{}{
			"flow_id":     "flow-123",
			"analysis_id": "analysis-456",
		},
		"file_info": map[string]interface{}{
			"attribute": map[string]interface{}{
				"os": "windows",
			},
		},
		"options": map[string]interface{}{
			"show_progress": true,
			"kafka_option": map[string]interface{}{
				"topic": "progress",
			},
		},
	}

	msg, err := formatter.FormatKafkaProgressMessage(parseDict, "pslist", 75.0)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if msg["flow_id"] != "flow-123" {
		t.Errorf("Expected flow_id 'flow-123', got '%v'", msg["flow_id"])
	}
	if msg["analysis_id"] != "analysis-456" {
		t.Errorf("Expected analysis_id 'analysis-456', got '%v'", msg["analysis_id"])
	}
	if msg["plugin"] != "pslist" {
		t.Errorf("Expected plugin 'pslist', got '%v'", msg["plugin"])
	}
	if msg["progress"] != 75.0 {
		t.Errorf("Expected progress 75.0, got '%v'", msg["progress"])
	}
	if msg["status"] != "running" {
		t.Errorf("Expected status 'running', got '%v'", msg["status"])
	}
}

func TestMessageFormatter_FormatKafkaDataMessage(t *testing.T) {
	formatter := NewMessageFormatter()

	parseDict := map[string]interface{}{
		"extra": map[string]interface{}{
			"flow_id":     "flow-123",
			"analysis_id": "analysis-456",
		},
		"file_info": map[string]interface{}{
			"attribute": map[string]interface{}{
				"os": "windows",
			},
		},
		"options": map[string]interface{}{
			"upload_option": map[string]interface{}{
				"bucket": "results",
			},
		},
	}

	msg, err := formatter.FormatKafkaDataMessage(parseDict, "pslist", "/results/pslist.json", true)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if msg["flow_id"] != "flow-123" {
		t.Errorf("Expected flow_id 'flow-123', got '%v'", msg["flow_id"])
	}
	if msg["plugin"] != "pslist" {
		t.Errorf("Expected plugin 'pslist', got '%v'", msg["plugin"])
	}
	if msg["result_path"] != "/results/pslist.json" {
		t.Errorf("Expected result_path '/results/pslist.json', got '%v'", msg["result_path"])
	}
	if msg["status"] != "completed" {
		t.Errorf("Expected status 'completed', got '%v'", msg["status"])
	}
	if msg["success"] != true {
		t.Errorf("Expected success true, got '%v'", msg["success"])
	}
}

func TestMessageFormatter_ValidateMessage(t *testing.T) {
	formatter := NewMessageFormatter()

	// 测试有效消息
	validMsg := map[string]interface{}{
		"flow_id":   "flow-123",
		"timestamp": int64(1234567890),
		"plugin":    "pslist",
		"status":    "running",
	}

	err := formatter.ValidateMessage(validMsg)
	if err != nil {
		t.Errorf("Expected no error for valid message, got %v", err)
	}

	// 测试缺少必需字段的消息
	invalidMsg := map[string]interface{}{
		"plugin": "pslist",
		"status": "running",
	}

	err = formatter.ValidateMessage(invalidMsg)
	if err == nil {
		t.Error("Expected error for message missing required fields")
	}

	// 测试无效时间戳格式
	invalidTimestampMsg := map[string]interface{}{
		"flow_id":   "flow-123",
		"timestamp": "invalid",
		"plugin":    "pslist",
	}

	err = formatter.ValidateMessage(invalidTimestampMsg)
	if err == nil {
		t.Error("Expected error for invalid timestamp format")
	}
}

func TestMessageFormatter_GetMessageType(t *testing.T) {
	formatter := NewMessageFormatter()

	tests := []struct {
		message      map[string]interface{}
		expectedType string
	}{
		{
			message:      map[string]interface{}{"status": "running"},
			expectedType: "progress",
		},
		{
			message:      map[string]interface{}{"status": "completed"},
			expectedType: "result",
		},
		{
			message:      map[string]interface{}{"status": "failed"},
			expectedType: "result",
		},
		{
			message:      map[string]interface{}{"status": "error"},
			expectedType: "error",
		},
		{
			message:      map[string]interface{}{"status": "started"},
			expectedType: "start",
		},
		{
			message:      map[string]interface{}{"progress": 50.0},
			expectedType: "progress",
		},
		{
			message:      map[string]interface{}{"result_path": "/path/to/result"},
			expectedType: "result",
		},
		{
			message:      map[string]interface{}{"plugin": "pslist"},
			expectedType: "unknown",
		},
	}

	for _, test := range tests {
		msgType := formatter.GetMessageType(test.message)
		if msgType != test.expectedType {
			t.Errorf("Expected message type '%s', got '%s' for message %v", test.expectedType, msgType, test.message)
		}
	}
}

func TestMessageFormatter_FormatTaskStatusMessage(t *testing.T) {
	formatter := NewMessageFormatter()

	createdAt := time.Now()
	startedAt := createdAt.Add(time.Second)
	completedAt := startedAt.Add(time.Minute)

	task := &models.Task{
		ID:          "task-123",
		FlowID:      "flow-123",
		Status:      models.TaskStatusCompleted,
		Progress:    100.0,
		CreatedAt:   createdAt,
		StartedAt:   &startedAt,
		CompletedAt: &completedAt,
		Request: &models.AnalysisRequest{
			Extra: map[string]interface{}{
				"analysis_id": "analysis-456",
			},
			FileInfo: map[string]interface{}{
				"attribute": map[string]interface{}{
					"os": "windows",
				},
			},
			Options: map[string]interface{}{
				"parse_items": map[string]interface{}{
					"pslist":  map[string]interface{}{},
					"netstat": map[string]interface{}{},
				},
			},
		},
		Metadata: map[string]interface{}{
			"total_plugins": 2,
		},
	}

	msg, err := formatter.FormatTaskStatusMessage(task)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if msg["flow_id"] != "flow-123" {
		t.Errorf("Expected flow_id 'flow-123', got '%v'", msg["flow_id"])
	}
	if msg["task_id"] != "task-123" {
		t.Errorf("Expected task_id 'task-123', got '%v'", msg["task_id"])
	}
	if msg["status"] != string(models.TaskStatusCompleted) {
		t.Errorf("Expected status '%s', got '%v'", string(models.TaskStatusCompleted), msg["status"])
	}
	if msg["progress"] != 100.0 {
		t.Errorf("Expected progress 100.0, got '%v'", msg["progress"])
	}
	if msg["analysis_id"] != "analysis-456" {
		t.Errorf("Expected analysis_id 'analysis-456', got '%v'", msg["analysis_id"])
	}
	if msg["os_type"] != "windows" {
		t.Errorf("Expected os_type 'windows', got '%v'", msg["os_type"])
	}
	if msg["plugin_count"] != 2 {
		t.Errorf("Expected plugin_count 2, got '%v'", msg["plugin_count"])
	}

	// 检查时间字段
	if _, exists := msg["created_at"]; !exists {
		t.Error("Expected created_at field to exist")
	}
	if _, exists := msg["started_at"]; !exists {
		t.Error("Expected started_at field to exist")
	}
	if _, exists := msg["completed_at"]; !exists {
		t.Error("Expected completed_at field to exist")
	}
	if _, exists := msg["duration"]; !exists {
		t.Error("Expected duration field to exist")
	}
}

func TestMessageFormatter_SerializeDeserialize(t *testing.T) {
	formatter := NewMessageFormatter()

	originalMsg := map[string]interface{}{
		"flow_id":   "flow-123",
		"plugin":    "pslist",
		"progress":  75.5,
		"timestamp": int64(1234567890),
		"nested": map[string]interface{}{
			"key": "value",
		},
	}

	// 序列化
	data, err := formatter.SerializeMessage(originalMsg)
	if err != nil {
		t.Fatalf("Expected no error during serialization, got %v", err)
	}

	// 反序列化
	deserializedMsg, err := formatter.DeserializeMessage(data)
	if err != nil {
		t.Fatalf("Expected no error during deserialization, got %v", err)
	}

	// 验证数据一致性
	if deserializedMsg["flow_id"] != originalMsg["flow_id"] {
		t.Errorf("Expected flow_id '%v', got '%v'", originalMsg["flow_id"], deserializedMsg["flow_id"])
	}
	if deserializedMsg["plugin"] != originalMsg["plugin"] {
		t.Errorf("Expected plugin '%v', got '%v'", originalMsg["plugin"], deserializedMsg["plugin"])
	}
	if deserializedMsg["progress"] != originalMsg["progress"] {
		t.Errorf("Expected progress '%v', got '%v'", originalMsg["progress"], deserializedMsg["progress"])
	}
}
