package messaging

import (
	"context"
	"fmt"
	"time"

	"memory-go/pkg/logger"

	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
)

// KafkaProducer Kafka 生产者
type KafkaProducer struct {
	producer *kafka.Producer
	config   *ProducerConfig
	logger   *logger.Logger
	stats    *MessageStats
}

// NewKafkaProducer 创建 Kafka 生产者
func NewKafkaProducer(config *ProducerConfig, log *logger.Logger) (*KafkaProducer, error) {
	// 构建 Kafka 配置 (使用 librdkafka 配置属性)
	kafkaConfig := &kafka.ConfigMap{
		"bootstrap.servers":                     joinStrings(config.BootstrapServers, ","),
		"client.id":                             "memory-go-producer",
		"acks":                                  config.Acks,
		"retries":                               config.Retries,
		"batch.num.messages":                    config.BatchSize,
		"linger.ms":                             config.Linger<PERSON>,
		"queue.buffering.max.messages":          int(config.BufferMemory / 1024), // 转换为消息数量
		"compression.type":                      config.Compression,
		"enable.idempotence":                    config.IdempotentWrites,
		"max.in.flight.requests.per.connection": config.MaxInFlight,
		"request.timeout.ms":                    int(config.RequestTimeout.Milliseconds()),
		"metadata.max.age.ms":                   30000,
		"socket.timeout.ms":                     10000,
	}

	// 创建生产者
	producer, err := kafka.NewProducer(kafkaConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create Kafka producer: %w", err)
	}

	kp := &KafkaProducer{
		producer: producer,
		config:   config,
		logger:   log,
		stats:    &MessageStats{},
	}

	// 启动事件处理 goroutine
	go kp.handleEvents()

	log.WithFields(map[string]interface{}{
		"bootstrap_servers": config.BootstrapServers,
		"acks":              config.Acks,
		"retries":           config.Retries,
	}).Info("Kafka producer initialized successfully")

	return kp, nil
}

// Send 发送单条消息
func (kp *KafkaProducer) Send(ctx context.Context, topic string, message *Message) error {
	// 构建 Kafka 消息
	kafkaMsg := &kafka.Message{
		TopicPartition: kafka.TopicPartition{
			Topic:     &topic,
			Partition: kafka.PartitionAny,
		},
		Value:     message.Value,
		Key:       []byte(message.Key),
		Headers:   convertHeaders(message.Headers),
		Timestamp: message.Timestamp,
	}

	// 如果指定了分区
	if message.Partition >= 0 {
		kafkaMsg.TopicPartition.Partition = message.Partition
	}

	// 发送消息
	deliveryChan := make(chan kafka.Event, 1)
	err := kp.producer.Produce(kafkaMsg, deliveryChan)
	if err != nil {
		kp.stats.Failed++
		kp.logger.WithError(err).WithFields(map[string]interface{}{
			"topic": topic,
			"key":   message.Key,
		}).Error("Failed to produce message")
		return fmt.Errorf("failed to produce message: %w", err)
	}

	// 等待投递结果
	select {
	case e := <-deliveryChan:
		if msg, ok := e.(*kafka.Message); ok {
			if msg.TopicPartition.Error != nil {
				kp.stats.Failed++
				kp.logger.WithError(msg.TopicPartition.Error).WithFields(map[string]interface{}{
					"topic":     topic,
					"key":       message.Key,
					"partition": msg.TopicPartition.Partition,
				}).Error("Message delivery failed")
				return fmt.Errorf("message delivery failed: %w", msg.TopicPartition.Error)
			}

			// 更新消息信息
			message.Partition = msg.TopicPartition.Partition
			message.Offset = int64(msg.TopicPartition.Offset)
			kp.stats.Sent++

			kp.logger.WithFields(map[string]interface{}{
				"topic":     topic,
				"key":       message.Key,
				"partition": msg.TopicPartition.Partition,
				"offset":    msg.TopicPartition.Offset,
			}).Debug("Message delivered successfully")
		}
	case <-ctx.Done():
		kp.stats.Failed++
		return ctx.Err()
	case <-time.After(kp.config.RequestTimeout):
		kp.stats.Failed++
		return fmt.Errorf("message delivery timeout")
	}

	return nil
}

// SendBatch 批量发送消息
func (kp *KafkaProducer) SendBatch(ctx context.Context, topic string, messages []*Message) error {
	if len(messages) == 0 {
		return nil
	}

	kp.logger.WithFields(map[string]interface{}{
		"topic": topic,
		"count": len(messages),
	}).Info("Sending batch messages")

	// 创建投递通道
	deliveryChan := make(chan kafka.Event, len(messages))

	// 批量发送消息
	for _, message := range messages {
		kafkaMsg := &kafka.Message{
			TopicPartition: kafka.TopicPartition{
				Topic:     &topic,
				Partition: kafka.PartitionAny,
			},
			Value:     message.Value,
			Key:       []byte(message.Key),
			Headers:   convertHeaders(message.Headers),
			Timestamp: message.Timestamp,
		}

		if message.Partition >= 0 {
			kafkaMsg.TopicPartition.Partition = message.Partition
		}

		err := kp.producer.Produce(kafkaMsg, deliveryChan)
		if err != nil {
			kp.stats.Failed++
			kp.logger.WithError(err).WithFields(map[string]interface{}{
				"topic": topic,
				"key":   message.Key,
			}).Error("Failed to produce batch message")
			return fmt.Errorf("failed to produce batch message: %w", err)
		}
	}

	// 等待所有消息投递完成
	successCount := 0
	for i := 0; i < len(messages); i++ {
		select {
		case e := <-deliveryChan:
			if msg, ok := e.(*kafka.Message); ok {
				if msg.TopicPartition.Error != nil {
					kp.stats.Failed++
					kp.logger.WithError(msg.TopicPartition.Error).WithFields(map[string]interface{}{
						"topic":     topic,
						"partition": msg.TopicPartition.Partition,
					}).Error("Batch message delivery failed")
				} else {
					kp.stats.Sent++
					successCount++
				}
			}
		case <-ctx.Done():
			kp.stats.Failed += int64(len(messages) - i)
			return ctx.Err()
		case <-time.After(kp.config.RequestTimeout):
			kp.stats.Failed += int64(len(messages) - i)
			return fmt.Errorf("batch message delivery timeout")
		}
	}

	kp.logger.WithFields(map[string]interface{}{
		"topic":   topic,
		"total":   len(messages),
		"success": successCount,
		"failed":  len(messages) - successCount,
	}).Info("Batch message delivery completed")

	return nil
}

// SendAsync 异步发送消息
func (kp *KafkaProducer) SendAsync(topic string, message *Message, callback func(error)) error {
	kafkaMsg := &kafka.Message{
		TopicPartition: kafka.TopicPartition{
			Topic:     &topic,
			Partition: kafka.PartitionAny,
		},
		Value:     message.Value,
		Key:       []byte(message.Key),
		Headers:   convertHeaders(message.Headers),
		Timestamp: message.Timestamp,
	}

	if message.Partition >= 0 {
		kafkaMsg.TopicPartition.Partition = message.Partition
	}

	// 异步发送
	deliveryChan := make(chan kafka.Event, 1)
	err := kp.producer.Produce(kafkaMsg, deliveryChan)
	if err != nil {
		kp.stats.Failed++
		if callback != nil {
			callback(err)
		}
		return fmt.Errorf("failed to produce async message: %w", err)
	}

	// 异步处理结果
	go func() {
		select {
		case e := <-deliveryChan:
			if msg, ok := e.(*kafka.Message); ok {
				if msg.TopicPartition.Error != nil {
					kp.stats.Failed++
					if callback != nil {
						callback(msg.TopicPartition.Error)
					}
				} else {
					kp.stats.Sent++
					message.Partition = msg.TopicPartition.Partition
					message.Offset = int64(msg.TopicPartition.Offset)
					if callback != nil {
						callback(nil)
					}
				}
			}
		case <-time.After(kp.config.RequestTimeout):
			kp.stats.Failed++
			if callback != nil {
				callback(fmt.Errorf("async message delivery timeout"))
			}
		}
	}()

	return nil
}

// Flush 刷新所有待发送的消息
func (kp *KafkaProducer) Flush(timeout time.Duration) error {
	remaining := kp.producer.Flush(int(timeout.Milliseconds()))
	if remaining > 0 {
		return fmt.Errorf("failed to flush %d messages within timeout", remaining)
	}
	return nil
}

// GetStats 获取统计信息
func (kp *KafkaProducer) GetStats() *MessageStats {
	return kp.stats
}

// Close 关闭生产者
func (kp *KafkaProducer) Close() error {
	kp.logger.Info("Closing Kafka producer")

	// 刷新待发送的消息
	kp.producer.Flush(5000) // 5 秒超时

	// 关闭生产者
	kp.producer.Close()

	kp.logger.WithFields(map[string]interface{}{
		"sent":   kp.stats.Sent,
		"failed": kp.stats.Failed,
	}).Info("Kafka producer closed")

	return nil
}

// handleEvents 处理 Kafka 事件
func (kp *KafkaProducer) handleEvents() {
	for e := range kp.producer.Events() {
		switch ev := e.(type) {
		case *kafka.Message:
			if ev.TopicPartition.Error != nil {
				kp.logger.WithError(ev.TopicPartition.Error).WithFields(map[string]interface{}{
					"topic":     *ev.TopicPartition.Topic,
					"partition": ev.TopicPartition.Partition,
				}).Error("Message delivery failed in event handler")
			}
		case kafka.Error:
			kp.logger.WithError(ev).Error("Kafka error event")
		default:
			kp.logger.WithField("event", fmt.Sprintf("%v", ev)).Debug("Kafka event")
		}
	}
}

// convertHeaders 转换消息头
func convertHeaders(headers map[string]string) []kafka.Header {
	if headers == nil {
		return nil
	}

	kafkaHeaders := make([]kafka.Header, 0, len(headers))
	for key, value := range headers {
		kafkaHeaders = append(kafkaHeaders, kafka.Header{
			Key:   key,
			Value: []byte(value),
		})
	}

	return kafkaHeaders
}

// joinStrings 连接字符串数组
func joinStrings(strs []string, sep string) string {
	if len(strs) == 0 {
		return ""
	}
	if len(strs) == 1 {
		return strs[0]
	}

	result := strs[0]
	for i := 1; i < len(strs); i++ {
		result += sep + strs[i]
	}
	return result
}
