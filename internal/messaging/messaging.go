package messaging

import (
	"context"
	"time"
)

// MessageProducer 消息生产者接口
type MessageProducer interface {
	// Send 发送单条消息
	Send(ctx context.Context, topic string, message *Message) error

	// SendBatch 批量发送消息
	SendBatch(ctx context.Context, topic string, messages []*Message) error

	// Close 关闭生产者
	Close() error
}

// MessageConsumer 消息消费者接口
type MessageConsumer interface {
	// Subscribe 订阅主题
	Subscribe(topics []string) error

	// Consume 消费消息
	Consume(ctx context.Context, handler MessageHandler) error

	// Close 关闭消费者
	Close() error
}

// MessageHandler 消息处理器
type MessageHandler func(message *Message) error

// Message 消息结构体
type Message struct {
	ID        string            `json:"id"`
	Topic     string            `json:"topic"`
	Key       string            `json:"key"`
	Value     []byte            `json:"value"`
	Headers   map[string]string `json:"headers"`
	Timestamp time.Time         `json:"timestamp"`
	Partition int32             `json:"partition"`
	Offset    int64             `json:"offset"`
}

// SendOptions 发送选项
type SendOptions struct {
	Key       string            `json:"key"`
	Headers   map[string]string `json:"headers"`
	Partition int32             `json:"partition"`
	Timeout   time.Duration     `json:"timeout"`
}

// ConsumeOptions 消费选项
type ConsumeOptions struct {
	GroupID        string        `json:"group_id"`
	AutoCommit     bool          `json:"auto_commit"`
	CommitInterval time.Duration `json:"commit_interval"`
	SessionTimeout time.Duration `json:"session_timeout"`
	OffsetReset    string        `json:"offset_reset"` // earliest, latest
}

// ProducerConfig 生产者配置
type ProducerConfig struct {
	BootstrapServers []string      `json:"bootstrap_servers"`
	Acks             string        `json:"acks"` // all, 1, 0
	Retries          int           `json:"retries"`
	BatchSize        int           `json:"batch_size"`
	LingerMs         int           `json:"linger_ms"`
	BufferMemory     int64         `json:"buffer_memory"`
	Compression      string        `json:"compression"` // none, gzip, snappy, lz4, zstd
	IdempotentWrites bool          `json:"idempotent_writes"`
	MaxInFlight      int           `json:"max_in_flight"`
	RequestTimeout   time.Duration `json:"request_timeout"`
}

// ConsumerConfig 消费者配置
type ConsumerConfig struct {
	BootstrapServers   []string      `json:"bootstrap_servers"`
	GroupID            string        `json:"group_id"`
	AutoOffsetReset    string        `json:"auto_offset_reset"`
	EnableAutoCommit   bool          `json:"enable_auto_commit"`
	AutoCommitInterval time.Duration `json:"auto_commit_interval"`
	SessionTimeout     time.Duration `json:"session_timeout"`
	HeartbeatInterval  time.Duration `json:"heartbeat_interval"`
	MaxPollRecords     int           `json:"max_poll_records"`
	FetchMinBytes      int           `json:"fetch_min_bytes"`
	FetchMaxWait       time.Duration `json:"fetch_max_wait"`
}

// MessageStats 消息统计
type MessageStats struct {
	Sent      int64 `json:"sent"`
	Failed    int64 `json:"failed"`
	Received  int64 `json:"received"`
	Processed int64 `json:"processed"`
	Errors    int64 `json:"errors"`
}

// TopicInfo 主题信息
type TopicInfo struct {
	Name       string `json:"name"`
	Partitions int32  `json:"partitions"`
	Replicas   int16  `json:"replicas"`
}

// PartitionInfo 分区信息
type PartitionInfo struct {
	Topic     string  `json:"topic"`
	Partition int32   `json:"partition"`
	Leader    int32   `json:"leader"`
	Replicas  []int32 `json:"replicas"`
	ISR       []int32 `json:"isr"`
}
