package models

import (
	"time"
)

// MemoryParseRequest 内存分析请求结构体，与 Python 版本保持一致
type MemoryParseRequest struct {
	FileInfo map[string]interface{} `json:"file_info" binding:"required"`
	Options  map[string]interface{} `json:"options" binding:"required"`
	Extra    map[string]interface{} `json:"extra" binding:"required"`
}

// FileInfo 文件信息结构体
type FileInfo struct {
	Type           string         `json:"type"`
	SubType        string         `json:"sub_type"`
	Attribute      FileAttribute  `json:"attribute"`
	OSS            OSSInfo        `json:"oss"`
	DownloadOption DownloadOption `json:"download_option"`
}

// FileAttribute 文件属性
type FileAttribute struct {
	OS OSInfo `json:"os"`
}

// OSInfo 操作系统信息
type OSInfo struct {
	Type string `json:"type"`
}

// OSSInfo 对象存储信息
type OSSInfo struct {
	File   string `json:"file"`
	Bucket string `json:"bucket"`
}

// DownloadOption 下载选项
type DownloadOption struct {
	Type string    `json:"type"`
	OSS  OSSConfig `json:"oss"`
}

// OSSConfig OSS 配置信息
type OSSConfig struct {
	Host string `json:"host"`
	AK   string `json:"ak"`
	SK   string `json:"sk"`
	SSL  bool   `json:"ssl"`
}

// AnalysisOptions 分析选项
type AnalysisOptions struct {
	UploadOption     UploadOption           `json:"upload_option"`
	Symbol           SymbolOption           `json:"symbol"`
	KafkaOption      KafkaOption            `json:"kafka_option"`
	ShowProgress     bool                   `json:"show_progress"`
	ParseItems       map[string]interface{} `json:"parse_items"`
	CustomParseItems map[string]interface{} `json:"custom_parse_items"`
}

// UploadOption 上传选项
type UploadOption struct {
	Type   string    `json:"type"`
	OSS    OSSConfig `json:"oss"`
	Bucket string    `json:"bucket"`
	Path   string    `json:"path"`
}

// SymbolOption 符号文件选项
type SymbolOption struct {
	Type     string    `json:"type"`
	OSS      OSSConfig `json:"oss"`
	Bucket   string    `json:"bucket"`
	FilePath string    `json:"file_path"`
}

// KafkaOption Kafka 选项
type KafkaOption struct {
	Topic []string `json:"topic"`
}

// ExtraInfo 额外信息
type ExtraInfo struct {
	AnalysisID string `json:"analysis_id"`
	FlowID     string `json:"flow_id"`
}

// TaskStatus 任务状态
type TaskStatus string

const (
	TaskStatusPending   TaskStatus = "pending"
	TaskStatusRunning   TaskStatus = "running"
	TaskStatusCompleted TaskStatus = "completed"
	TaskStatusFailed    TaskStatus = "failed"
	TaskStatusCancelled TaskStatus = "cancelled"
)

// Task 任务结构体
type Task struct {
	ID          string                 `json:"id"`
	FlowID      string                 `json:"flow_id"`
	Status      TaskStatus             `json:"status"`
	Request     *MemoryParseRequest    `json:"request"`
	CreatedAt   time.Time              `json:"created_at"`
	StartedAt   *time.Time             `json:"started_at,omitempty"`
	CompletedAt *time.Time             `json:"completed_at,omitempty"`
	Progress    float64                `json:"progress"`
	Error       string                 `json:"error,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// VolatilityResult Volatility 执行结果
type VolatilityResult struct {
	Plugin        string                   `json:"plugin"`
	Success       bool                     `json:"success"`
	RawData       []map[string]interface{} `json:"raw_data"`
	ProcessedData []map[string]interface{} `json:"processed_data"`
	FileOutputs   []FileOutput             `json:"file_outputs"`
	Error         string                   `json:"error,omitempty"`
	Duration      time.Duration            `json:"duration"`
}

// FileOutput 文件输出信息
type FileOutput struct {
	LocalPath    string            `json:"local_path"`
	RemotePath   string            `json:"remote_path"`
	Size         int64             `json:"size"`
	Hashes       map[string]string `json:"hashes"`
	UploadStatus string            `json:"upload_status"`
}

// ProcessedResult 处理后的结果
type ProcessedResult struct {
	Plugin      string                   `json:"plugin"`
	Data        []map[string]interface{} `json:"data"`
	FileOutputs []FileOutput             `json:"file_outputs"`
	UploadPath  string                   `json:"upload_path"`
}

// APIResponse API 响应结构体
type APIResponse struct {
	Code        int         `json:"code"`
	Message     string      `json:"message"`
	ParseResult bool        `json:"parse_result"`
	Desc        string      `json:"desc"`
	Data        interface{} `json:"data,omitempty"`
}

// ProgressMessage 进度消息
type ProgressMessage struct {
	FlowID    string  `json:"flow_id"`
	Plugin    string  `json:"plugin"`
	Progress  float64 `json:"progress"`
	Status    string  `json:"status"`
	Message   string  `json:"message"`
	Timestamp int64   `json:"timestamp"`
}

// DataMessage 数据消息
type DataMessage struct {
	FlowID     string `json:"flow_id"`
	Plugin     string `json:"plugin"`
	ResultPath string `json:"result_path"`
	Status     string `json:"status"`
	Timestamp  int64  `json:"timestamp"`
}
