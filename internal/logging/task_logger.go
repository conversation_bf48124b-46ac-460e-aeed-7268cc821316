package logging

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// TaskLogger 任务专用日志器
type TaskLogger struct {
	taskID     string
	analysisID string
	logFile    *os.File
	logger     *logrus.Logger
	mutex      sync.Mutex
	closed     bool
}

// TaskLoggerManager 任务日志管理器
type TaskLoggerManager struct {
	logDir      string
	taskLoggers map[string]*TaskLogger
	mutex       sync.RWMutex
}

// NewTaskLoggerManager 创建任务日志管理器
func NewTaskLoggerManager(logDir string) *TaskLoggerManager {
	return &TaskLoggerManager{
		logDir:      logDir,
		taskLoggers: make(map[string]*TaskLogger),
	}
}

// CreateTaskLogger 为任务创建专用日志器
func (tlm *TaskLoggerManager) CreateTaskLogger(taskID, analysisID string) (*TaskLogger, error) {
	tlm.mutex.Lock()
	defer tlm.mutex.Unlock()

	// 检查是否已存在
	if existing, exists := tlm.taskLoggers[taskID]; exists {
		return existing, nil
	}

	// 确保日志目录存在
	if err := os.MkdirAll(tlm.logDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create log directory: %w", err)
	}

	// 创建日志文件，使用 analysis_id 作为主要标识符
	logFileName := fmt.Sprintf("%s.log", analysisID)
	logFilePath := filepath.Join(tlm.logDir, logFileName)

	logFile, err := os.OpenFile(logFilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, fmt.Errorf("failed to create log file: %w", err)
	}

	// 创建专用的 logrus 实例
	taskLogger := logrus.New()
	taskLogger.SetOutput(logFile)
	taskLogger.SetLevel(logrus.InfoLevel)
	taskLogger.SetFormatter(&logrus.TextFormatter{
		FullTimestamp:   true,
		TimestampFormat: "2006-01-02 15:04:05",
		DisableColors:   true,
	})

	tl := &TaskLogger{
		taskID:     taskID,
		analysisID: analysisID,
		logFile:    logFile,
		logger:     taskLogger,
	}

	// 写入初始日志
	tl.LogInfo("=== Memory Analysis Started ===")
	tl.LogInfo("Task ID: %s", taskID)
	tl.LogInfo("Analysis ID: %s", analysisID)
	tl.LogInfo("Start Time: %s", time.Now().Format("2006-01-02 15:04:05"))
	tl.LogInfo("=====================================")

	tlm.taskLoggers[taskID] = tl
	return tl, nil
}

// GetTaskLogger 获取任务日志器
func (tlm *TaskLoggerManager) GetTaskLogger(taskID string) (*TaskLogger, bool) {
	tlm.mutex.RLock()
	defer tlm.mutex.RUnlock()
	tl, exists := tlm.taskLoggers[taskID]
	return tl, exists
}

// CloseTaskLogger 关闭任务日志器
func (tlm *TaskLoggerManager) CloseTaskLogger(taskID string) error {
	tlm.mutex.Lock()
	defer tlm.mutex.Unlock()

	if tl, exists := tlm.taskLoggers[taskID]; exists {
		err := tl.Close()
		delete(tlm.taskLoggers, taskID)
		return err
	}
	return nil
}

// GetTaskLogContent 获取任务日志内容
func (tlm *TaskLoggerManager) GetTaskLogContent(analysisID string) (string, error) {
	// 直接根据 analysis_id 构建日志文件路径
	logFileName := fmt.Sprintf("%s.log", analysisID)
	logFilePath := filepath.Join(tlm.logDir, logFileName)

	// 检查文件是否存在
	if _, err := os.Stat(logFilePath); os.IsNotExist(err) {
		return "", fmt.Errorf("log file not found for analysis_id: %s", analysisID)
	}

	// 读取日志文件内容
	content, err := os.ReadFile(logFilePath)
	if err != nil {
		return "", fmt.Errorf("failed to read log file: %w", err)
	}

	return string(content), nil
}

// TaskLogger 方法

// LogInfo 记录信息日志
func (tl *TaskLogger) LogInfo(format string, args ...interface{}) {
	tl.mutex.Lock()
	defer tl.mutex.Unlock()
	if !tl.closed {
		tl.logger.Infof(format, args...)
	}
}

// LogError 记录错误日志
func (tl *TaskLogger) LogError(format string, args ...interface{}) {
	tl.mutex.Lock()
	defer tl.mutex.Unlock()
	if !tl.closed {
		tl.logger.Errorf(format, args...)
	}
}

// LogWarn 记录警告日志
func (tl *TaskLogger) LogWarn(format string, args ...interface{}) {
	tl.mutex.Lock()
	defer tl.mutex.Unlock()
	if !tl.closed {
		tl.logger.Warnf(format, args...)
	}
}

// LogDebug 记录调试日志
func (tl *TaskLogger) LogDebug(format string, args ...interface{}) {
	tl.mutex.Lock()
	defer tl.mutex.Unlock()
	if !tl.closed {
		tl.logger.Debugf(format, args...)
	}
}

// LogPluginStart 记录插件开始
func (tl *TaskLogger) LogPluginStart(pluginName string) {
	tl.LogInfo("--- Starting plugin: %s ---", pluginName)
}

// LogPluginComplete 记录插件完成
func (tl *TaskLogger) LogPluginComplete(pluginName string, duration time.Duration, recordCount int) {
	tl.LogInfo("--- Plugin %s completed in %v, processed %d records ---", pluginName, duration, recordCount)
}

// LogPluginError 记录插件错误
func (tl *TaskLogger) LogPluginError(pluginName string, err error) {
	tl.LogError("--- Plugin %s failed: %v ---", pluginName, err)
}

// LogVolatilityCommand 记录 Volatility 命令
func (tl *TaskLogger) LogVolatilityCommand(command string) {
	tl.LogInfo("Executing Volatility command: %s", command)
}

// LogVolatilityOutput 记录 Volatility 输出
func (tl *TaskLogger) LogVolatilityOutput(output string) {
	tl.LogDebug("Volatility output: %s", output)
}

// LogFileUpload 记录文件上传
func (tl *TaskLogger) LogFileUpload(fileName, uploadPath string, size int64) {
	tl.LogInfo("Uploaded file: %s -> %s (size: %d bytes)", fileName, uploadPath, size)
}

// LogProgress 记录进度
func (tl *TaskLogger) LogProgress(progress float64, message string) {
	tl.LogInfo("Progress: %.1f%% - %s", progress, message)
}

// LogTaskComplete 记录任务完成
func (tl *TaskLogger) LogTaskComplete(duration time.Duration, success bool) {
	tl.LogInfo("=====================================")
	tl.LogInfo("=== Memory Analysis Completed ===")
	tl.LogInfo("Duration: %v", duration)
	tl.LogInfo("Success: %t", success)
	tl.LogInfo("End Time: %s", time.Now().Format("2006-01-02 15:04:05"))
	tl.LogInfo("=====================================")
}

// Close 关闭日志器
func (tl *TaskLogger) Close() error {
	tl.mutex.Lock()
	defer tl.mutex.Unlock()

	if tl.closed {
		return nil
	}

	tl.closed = true
	if tl.logFile != nil {
		return tl.logFile.Close()
	}
	return nil
}

// GetLogFilePath 获取日志文件路径
func (tl *TaskLogger) GetLogFilePath() string {
	if tl.logFile != nil {
		return tl.logFile.Name()
	}
	return ""
}

// GetWriter 获取写入器（用于重定向其他输出）
func (tl *TaskLogger) GetWriter() io.Writer {
	return tl.logFile
}

// 全局任务日志管理器
var GlobalTaskLoggerManager *TaskLoggerManager

// InitTaskLoggerManager 初始化全局任务日志管理器
func InitTaskLoggerManager(logDir string) {
	// 确保日志目录存在
	if err := os.MkdirAll(logDir, 0755); err != nil {
		fmt.Printf("Warning: failed to create log directory %s: %v\n", logDir, err)
	}

	GlobalTaskLoggerManager = NewTaskLoggerManager(logDir)
}

// CreateTaskLogger 创建任务日志器（便捷函数）
func CreateTaskLogger(taskID, analysisID string) (*TaskLogger, error) {
	if GlobalTaskLoggerManager == nil {
		return nil, fmt.Errorf("task logger manager not initialized")
	}
	return GlobalTaskLoggerManager.CreateTaskLogger(taskID, analysisID)
}

// GetTaskLogger 获取任务日志器（便捷函数）
func GetTaskLogger(taskID string) (*TaskLogger, bool) {
	if GlobalTaskLoggerManager == nil {
		return nil, false
	}
	return GlobalTaskLoggerManager.GetTaskLogger(taskID)
}

// CloseTaskLogger 关闭任务日志器（便捷函数）
func CloseTaskLogger(taskID string) error {
	if GlobalTaskLoggerManager == nil {
		return fmt.Errorf("task logger manager not initialized")
	}
	return GlobalTaskLoggerManager.CloseTaskLogger(taskID)
}

// GetTaskLogContent 获取任务日志内容（便捷函数）
func GetTaskLogContent(analysisID string) (string, error) {
	if GlobalTaskLoggerManager == nil {
		return "", fmt.Errorf("task logger manager not initialized")
	}
	return GlobalTaskLoggerManager.GetTaskLogContent(analysisID)
}
