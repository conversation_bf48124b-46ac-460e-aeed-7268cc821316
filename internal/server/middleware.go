package server

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
)

// RequestIDMiddleware 添加请求 ID 中间件
func (s *Server) RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.<PERSON>eader("X-Request-ID")
		if requestID == "" {
			// 简单的请求 ID 生成
			requestID = generateRequestID()
		}
		c.<PERSON><PERSON>("X-Request-ID", requestID)
		c.Set("request_id", requestID)
		c.Next()
	}
}

// generateRequestID 生成简单的请求 ID
func generateRequestID() string {
	return fmt.Sprintf("req_%d", time.Now().UnixNano())
}

// LoggingMiddleware 结构化日志中间件
func (s *Server) LoggingMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		requestID, _ := param.Keys["request_id"].(string)

		// 使用新的 HTTP 请求日志方法
		s.logger.LogHTTPRequest(
			param.Method,
			param.Path,
			param.Request.UserAgent(),
			param.ClientIP,
			param.StatusCode,
			param.Latency,
			requestID,
		)

		return ""
	})
}

// ErrorHandlingMiddleware 错误处理中间件
func (s *Server) ErrorHandlingMiddleware() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		requestID, _ := c.Get("request_id")

		s.logger.WithFields(map[string]interface{}{
			"request_id": requestID,
			"error":      recovered,
		}).Error("Panic recovered")

		c.JSON(500, gin.H{
			"code":    500,
			"message": "Internal server error",
			"error":   "An unexpected error occurred",
		})
	})
}

// CORSMiddleware CORS 中间件
func (s *Server) CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Request-ID")
		c.Header("Access-Control-Expose-Headers", "X-Request-ID")
		c.Header("Access-Control-Max-Age", "86400")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// RateLimitMiddleware 简单的速率限制中间件
func (s *Server) RateLimitMiddleware() gin.HandlerFunc {
	// 简单的内存速率限制器
	clients := make(map[string][]time.Time)
	const maxRequests = 100
	const timeWindow = time.Minute

	return func(c *gin.Context) {
		clientIP := c.ClientIP()
		now := time.Now()

		// 清理过期的请求记录
		if requests, exists := clients[clientIP]; exists {
			var validRequests []time.Time
			for _, reqTime := range requests {
				if now.Sub(reqTime) < timeWindow {
					validRequests = append(validRequests, reqTime)
				}
			}
			clients[clientIP] = validRequests
		}

		// 检查速率限制
		if len(clients[clientIP]) >= maxRequests {
			c.JSON(429, gin.H{
				"code":    429,
				"message": "Too many requests",
				"error":   "Rate limit exceeded",
			})
			c.Abort()
			return
		}

		// 记录当前请求
		clients[clientIP] = append(clients[clientIP], now)
		c.Next()
	}
}
