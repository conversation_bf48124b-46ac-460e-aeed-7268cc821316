package server

import (
	"context"
	"fmt"
	"time"

	"memory-go/internal/config"
	"memory-go/internal/engine"
	"memory-go/internal/logging"
	"memory-go/internal/models"
	"memory-go/internal/monitoring"
	"memory-go/pkg/logger"

	"github.com/gin-gonic/gin"
)

// Server HTTP 服务器
type Server struct {
	config        *config.Config
	logger        *logger.Logger
	router        *gin.Engine
	engine        *engine.AnalysisEngine
	startTime     time.Time
	healthChecker *monitoring.HealthChecker
	metrics       *monitoring.MetricsRegistry
}

// New 创建新的服务器实例
func New(cfg *config.Config, log *logger.Logger) (*Server, error) {
	// 设置 Gin 模式
	if cfg.Logging.Level == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建分析引擎
	analysisEngine := engine.New(cfg, log)

	// 创建监控组件
	healthChecker := monitoring.NewHealthChecker(5 * time.Second)
	metricsRegistry := monitoring.NewMetricsRegistry()

	server := &Server{
		config:        cfg,
		logger:        log,
		router:        gin.New(),
		engine:        analysisEngine,
		startTime:     time.Now(),
		healthChecker: healthChecker,
		metrics:       metricsRegistry,
	}

	// 初始化监控
	server.initializeMonitoring()

	// 设置中间件
	server.setupMiddleware()

	// 设置路由
	server.setupRoutes()

	return server, nil
}

// Router 返回 Gin 路由器
func (s *Server) Router() *gin.Engine {
	return s.router
}

// setupMiddleware 设置中间件
func (s *Server) setupMiddleware() {
	// 请求 ID 中间件
	s.router.Use(s.RequestIDMiddleware())

	// 结构化日志中间件
	s.router.Use(s.LoggingMiddleware())

	// 错误处理中间件
	s.router.Use(s.ErrorHandlingMiddleware())

	// CORS 中间件
	s.router.Use(s.CORSMiddleware())

	// 速率限制中间件
	s.router.Use(s.RateLimitMiddleware())
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// API v1 路由组
	v1 := s.router.Group("/v1")
	{
		// 健康检查和系统信息
		v1.GET("/health", s.handleHealth)
		v1.GET("/health/detailed", s.handleDetailedHealth)
		v1.GET("/metrics", s.handleMetrics)
		v1.GET("/version", s.handleVersion)
		v1.GET("/status", s.handleStatus)

		// 内存分析 API
		v1.POST("/memory_parse", s.handleMemoryParse)
		v1.DELETE("/memory_parse", s.handleMemoryParseCancel)
		v1.GET("/memory_parse/log/:analysis_id", s.handleMemoryParseLog)

		// 任务管理 API
		v1.GET("/tasks", s.handleListTasks)
		v1.GET("/tasks/:flow_id", s.handleGetTask)
		v1.DELETE("/tasks/:flow_id", s.handleCancelTask)
		v1.GET("/tasks/status/:status", s.handleGetTasksByStatus)
		v1.GET("/tasks/metrics", s.handleGetTaskMetrics)
		v1.POST("/tasks/cleanup", s.handleCleanupTasks)

		// 调度器管理 API
		v1.GET("/scheduler/stats", s.handleGetSchedulerStats)
		v1.GET("/scheduler/queue", s.handleGetQueueStatus)
		v1.POST("/scheduler/config", s.handleUpdateSchedulerConfig)
	}
}

// handleHealth 健康检查处理器
func (s *Server) handleHealth(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":  "ok",
		"service": "memory-go",
	})
}

// handleMemoryParse 内存分析处理器
func (s *Server) handleMemoryParse(c *gin.Context) {
	var req models.MemoryParseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		s.logger.WithError(err).Error("Invalid request format")
		c.JSON(400, models.APIResponse{
			Code:        400,
			Message:     "parameter error",
			ParseResult: false,
			Desc:        err.Error(),
		})
		return
	}

	// 记录请求
	s.logger.WithFields(map[string]interface{}{
		"request": req,
	}).Info("Received memory parse request")

	// 检查是否已存在相同的 flow_id
	if extra, ok := req.Extra["flow_id"].(string); ok && extra != "" {
		if _, err := s.engine.GetTaskStatus(extra); err == nil {
			c.JSON(400, models.APIResponse{
				Code:        400,
				Message:     "parameter error",
				ParseResult: false,
				Desc:        "The serial ID already exists",
			})
			return
		}
	}

	// 启动分析
	if err := s.engine.ProcessMemoryDump(c.Request.Context(), &req); err != nil {
		s.logger.WithError(err).Error("Failed to start analysis")
		c.JSON(400, models.APIResponse{
			Code:        400,
			Message:     "analysis start error",
			ParseResult: false,
			Desc:        err.Error(),
		})
		return
	}

	c.JSON(200, models.APIResponse{
		Code:        0,
		Message:     "",
		ParseResult: true,
		Desc:        "",
	})
}

// handleMemoryParseCancel 取消内存分析处理器
func (s *Server) handleMemoryParseCancel(c *gin.Context) {
	var req models.MemoryParseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		s.logger.WithError(err).Error("Invalid cancel request format")
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}

	flowID, ok := req.Extra["flow_id"].(string)
	if !ok || flowID == "" {
		c.JSON(400, gin.H{"error": "Missing flow_id"})
		return
	}

	if err := s.engine.CancelTask(flowID); err != nil {
		s.logger.WithError(err).WithField("flow_id", flowID).Error("Failed to cancel task")
		c.JSON(404, gin.H{"error": err.Error()})
		return
	}

	c.JSON(200, gin.H{
		"message": fmt.Sprintf("Task for flow_id %s terminated.", flowID),
	})
}

// handleMemoryParseLog 获取分析日志处理器
func (s *Server) handleMemoryParseLog(c *gin.Context) {
	analysisID := c.Param("analysis_id")

	if analysisID == "" {
		c.JSON(400, gin.H{
			"code":    400,
			"message": "analysis_id is required",
		})
		return
	}

	// 获取任务日志内容
	logContent, err := logging.GetTaskLogContent(analysisID)
	if err != nil {
		s.logger.WithError(err).WithField("analysis_id", analysisID).Error("Failed to get task log content")
		c.JSON(404, gin.H{
			"code":    404,
			"message": "log not found",
			"desc":    err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"code":        0,
		"message":     "success",
		"log_content": logContent,
	})
}

// handleVersion 版本信息处理器
func (s *Server) handleVersion(c *gin.Context) {
	c.JSON(200, gin.H{
		"service":    "memory-go",
		"version":    "1.0.0",
		"build":      "development",
		"go_version": "1.21",
	})
}

// handleStatus 系统状态处理器
func (s *Server) handleStatus(c *gin.Context) {
	activeTasks := s.engine.ListActiveTasks()
	queueStatus := s.engine.GetQueueStatus()
	taskMetrics := s.engine.GetTaskMetrics()

	c.JSON(200, gin.H{
		"status":       "running",
		"uptime":       time.Since(s.startTime).Seconds(),
		"active_tasks": len(activeTasks),
		"queue_status": queueStatus,
		"task_metrics": taskMetrics,
		"config": gin.H{
			"max_workers": s.config.Volatility.MaxWorkers,
			"timeout":     s.config.Volatility.Timeout,
		},
	})
}

// handleListTasks 列出任务处理器
func (s *Server) handleListTasks(c *gin.Context) {
	tasks := s.engine.ListActiveTasks()
	c.JSON(200, gin.H{
		"tasks": tasks,
		"count": len(tasks),
	})
}

// handleGetTask 获取单个任务处理器
func (s *Server) handleGetTask(c *gin.Context) {
	flowID := c.Param("flow_id")

	task, err := s.engine.GetTaskStatus(flowID)
	if err != nil {
		c.JSON(404, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"task": task,
	})
}

// handleCancelTask 取消单个任务处理器
func (s *Server) handleCancelTask(c *gin.Context) {
	flowID := c.Param("flow_id")

	if err := s.engine.CancelTask(flowID); err != nil {
		s.logger.WithError(err).WithField("flow_id", flowID).Error("Failed to cancel task")
		c.JSON(404, gin.H{"error": err.Error()})
		return
	}

	c.JSON(200, gin.H{
		"message": fmt.Sprintf("Task %s cancelled successfully", flowID),
	})
}

// handleGetTasksByStatus 根据状态获取任务处理器
func (s *Server) handleGetTasksByStatus(c *gin.Context) {
	statusStr := c.Param("status")

	var status models.TaskStatus
	switch statusStr {
	case "pending":
		status = models.TaskStatusPending
	case "running":
		status = models.TaskStatusRunning
	case "completed":
		status = models.TaskStatusCompleted
	case "failed":
		status = models.TaskStatusFailed
	case "cancelled":
		status = models.TaskStatusCancelled
	default:
		c.JSON(400, gin.H{
			"error": "Invalid status. Valid values: pending, running, completed, failed, cancelled",
		})
		return
	}

	tasks := s.engine.GetTasksByStatus(status)
	c.JSON(200, gin.H{
		"status": statusStr,
		"tasks":  tasks,
		"count":  len(tasks),
	})
}

// handleGetTaskMetrics 获取任务指标处理器
func (s *Server) handleGetTaskMetrics(c *gin.Context) {
	metrics := s.engine.GetTaskMetrics()

	// 添加服务器运行时间
	metrics["server_uptime"] = time.Since(s.startTime).Seconds()

	c.JSON(200, gin.H{
		"metrics": metrics,
	})
}

// handleCleanupTasks 清理已完成任务处理器
func (s *Server) handleCleanupTasks(c *gin.Context) {
	// 解析清理参数
	var req struct {
		OlderThanHours int `json:"older_than_hours" binding:"min=1"`
	}

	// 设置默认值
	req.OlderThanHours = 24 // 默认清理24小时前的任务

	if err := c.ShouldBindJSON(&req); err != nil {
		// 如果解析失败，使用默认值
		s.logger.WithError(err).Warn("Failed to parse cleanup request, using default values")
	}

	olderThan := time.Duration(req.OlderThanHours) * time.Hour
	cleanedCount := s.engine.CleanupCompletedTasks(olderThan)

	c.JSON(200, gin.H{
		"message":       "Task cleanup completed",
		"cleaned_count": cleanedCount,
		"older_than":    fmt.Sprintf("%d hours", req.OlderThanHours),
	})
}

// handleGetSchedulerStats 获取调度器统计信息处理器
func (s *Server) handleGetSchedulerStats(c *gin.Context) {
	stats := s.engine.GetSchedulerStats()
	c.JSON(200, gin.H{
		"scheduler_stats": stats,
	})
}

// handleGetQueueStatus 获取队列状态处理器
func (s *Server) handleGetQueueStatus(c *gin.Context) {
	queueStatus := s.engine.GetQueueStatus()
	c.JSON(200, gin.H{
		"queue_status": queueStatus,
	})
}

// handleUpdateSchedulerConfig 更新调度器配置处理器
func (s *Server) handleUpdateSchedulerConfig(c *gin.Context) {
	var req struct {
		MaxConcurrentTasks int `json:"max_concurrent_tasks" binding:"required,min=1,max=50"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	if err := s.engine.SetMaxConcurrentTasks(req.MaxConcurrentTasks); err != nil {
		s.logger.WithError(err).Error("Failed to update scheduler config")
		c.JSON(500, gin.H{
			"error":   "Failed to update scheduler configuration",
			"details": err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"message":              "Scheduler configuration updated successfully",
		"max_concurrent_tasks": req.MaxConcurrentTasks,
	})
}

// initializeMonitoring 初始化监控
func (s *Server) initializeMonitoring() {
	// 注册基础指标
	s.registerMetrics()

	// 注册健康检查
	s.registerHealthChecks()
}

// registerMetrics 注册指标
func (s *Server) registerMetrics() {
	// HTTP 请求指标
	httpRequestsTotal := monitoring.NewCounter("http_requests_total", map[string]string{
		"service": "memory-go",
	})
	s.metrics.Register(httpRequestsTotal)

	httpRequestDuration := monitoring.NewHistogram("http_request_duration_seconds",
		[]float64{0.1, 0.5, 1.0, 2.5, 5.0, 10.0},
		map[string]string{"service": "memory-go"})
	s.metrics.Register(httpRequestDuration)

	// 任务指标
	tasksTotal := monitoring.NewCounter("tasks_total", map[string]string{
		"service": "memory-go",
	})
	s.metrics.Register(tasksTotal)

	tasksActive := monitoring.NewGauge("tasks_active", map[string]string{
		"service": "memory-go",
	})
	s.metrics.Register(tasksActive)

	taskDuration := monitoring.NewHistogram("task_duration_seconds",
		[]float64{60, 300, 600, 1800, 3600, 7200},
		map[string]string{"service": "memory-go"})
	s.metrics.Register(taskDuration)

	// 系统指标
	memoryUsage := monitoring.NewGauge("memory_usage_bytes", map[string]string{
		"service": "memory-go",
	})
	s.metrics.Register(memoryUsage)

	goroutines := monitoring.NewGauge("goroutines_count", map[string]string{
		"service": "memory-go",
	})
	s.metrics.Register(goroutines)
}

// registerHealthChecks 注册健康检查
func (s *Server) registerHealthChecks() {
	// 基础健康检查
	s.healthChecker.Register(&BasicHealthCheck{
		name: "basic",
	})

	// 引擎健康检查
	s.healthChecker.Register(&EngineHealthCheck{
		name:   "analysis_engine",
		engine: s.engine,
	})

	// 可以添加更多健康检查，如数据库、外部服务等
}

// handleDetailedHealth 详细健康检查处理器
func (s *Server) handleDetailedHealth(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 10*time.Second)
	defer cancel()

	results := s.healthChecker.CheckAll(ctx)
	overallStatus := s.healthChecker.GetOverallStatus()

	response := map[string]interface{}{
		"status":    overallStatus,
		"timestamp": time.Now().Format(time.RFC3339),
		"checks":    results,
		"uptime":    time.Since(s.startTime).Seconds(),
	}

	statusCode := 200
	if overallStatus == monitoring.HealthStatusUnhealthy {
		statusCode = 503
	} else if overallStatus == monitoring.HealthStatusDegraded {
		statusCode = 200 // 或者可以使用 207 Multi-Status
	}

	c.JSON(statusCode, response)
}

// handleMetrics 指标处理器
func (s *Server) handleMetrics(c *gin.Context) {
	// 更新实时指标
	s.updateRuntimeMetrics()

	metrics := s.metrics.ToMap()

	c.JSON(200, gin.H{
		"metrics":   metrics,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// updateRuntimeMetrics 更新运行时指标
func (s *Server) updateRuntimeMetrics() {
	// 更新活跃任务数
	if tasksActive, exists := s.metrics.Get("tasks_active"); exists {
		if gauge, ok := tasksActive.(*monitoring.Gauge); ok {
			activeTasks := s.engine.ListActiveTasks()
			gauge.Set(float64(len(activeTasks)))
		}
	}

	// 可以添加更多运行时指标更新
}

// BasicHealthCheck 基础健康检查
type BasicHealthCheck struct {
	name string
}

func (bhc *BasicHealthCheck) Name() string {
	return bhc.name
}

func (bhc *BasicHealthCheck) Check(ctx context.Context) monitoring.HealthCheckResult {
	return monitoring.HealthCheckResult{
		Status:  monitoring.HealthStatusHealthy,
		Message: "Service is running",
		Details: map[string]interface{}{
			"service": "memory-go",
		},
	}
}

// EngineHealthCheck 分析引擎健康检查
type EngineHealthCheck struct {
	name   string
	engine *engine.AnalysisEngine
}

func (ehc *EngineHealthCheck) Name() string {
	return ehc.name
}

func (ehc *EngineHealthCheck) Check(ctx context.Context) monitoring.HealthCheckResult {
	// 检查引擎状态
	activeTasks := ehc.engine.ListActiveTasks()
	queueStatus := ehc.engine.GetQueueStatus()

	details := map[string]interface{}{
		"active_tasks": len(activeTasks),
		"queue_status": queueStatus,
	}

	// 简单的健康检查逻辑
	if queueEnabled, ok := queueStatus["scheduler_enabled"].(bool); ok && !queueEnabled {
		return monitoring.HealthCheckResult{
			Status:  monitoring.HealthStatusDegraded,
			Message: "Task scheduler is not enabled",
			Details: details,
		}
	}

	return monitoring.HealthCheckResult{
		Status:  monitoring.HealthStatusHealthy,
		Message: "Analysis engine is healthy",
		Details: details,
	}
}
