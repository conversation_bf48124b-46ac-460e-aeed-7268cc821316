# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build directory
bin/
dist/

# Log files
*.log
logs/

# Temporary files
tmp/
temp/
*.tmp

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Configuration files with secrets
configs/memory_analyzer.yaml
!configs/memory_analyzer.yaml.example

# Test coverage
coverage.out
coverage.html

# Air (hot reload) temporary files
tmp/

# Docker
.dockerignore

# Environment files
.env
.env.local
.env.*.local

# Memory dumps and analysis results
*.bin
*.dump
*.dmp
results/
dumps/
dump_memory/
symbols/
memory-go/